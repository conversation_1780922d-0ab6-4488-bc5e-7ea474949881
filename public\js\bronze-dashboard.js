/**
 * Bronze Plan Dashboard JavaScript
 * Handles AJAX interactions and dynamic content
 */

// CSRF Token setup for <PERSON><PERSON>
const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

// Utility function to show notifications
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-600 text-white' : 
        type === 'error' ? 'bg-red-600 text-white' : 
        'bg-blue-600 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

// Create Short Link functionality
function initCreateLinkForm() {
    const form = document.getElementById('create-link-form');
    const urlInput = document.getElementById('url-input');
    const submitBtn = document.getElementById('create-link-btn');
    
    if (!form || !urlInput || !submitBtn) return;
    
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const url = urlInput.value.trim();
        if (!url) {
            showNotification('Please enter a valid URL', 'error');
            return;
        }
        
        // Disable button and show loading
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating...';
        
        try {
            const response = await fetch('/bronze-plan/create-link', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({ url })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showNotification(data.message, 'success');
                urlInput.value = '';
                
                // Show created link info
                if (data.data) {
                    showCreatedLinkInfo(data.data);
                }
                
                // Refresh stats if on dashboard
                refreshDashboardStats();
            } else {
                showNotification(data.message, 'error');
                
                if (data.upgrade_required) {
                    showUpgradeModal();
                }
            }
        } catch (error) {
            showNotification('An error occurred. Please try again.', 'error');
            console.error('Error:', error);
        } finally {
            // Reset button
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-magic mr-2"></i>Shorten URL';
        }
    });
}

// Show created link information
function showCreatedLinkInfo(linkData) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/50';
    modal.innerHTML = `
        <div class="glass-effect rounded-xl p-6 m-4 max-w-md w-full">
            <div class="text-center">
                <i class="fas fa-check-circle text-green-400 text-4xl mb-4"></i>
                <h3 class="text-xl font-semibold mb-4">Link Created Successfully!</h3>
                
                <div class="bg-white/10 rounded-lg p-4 mb-4">
                    <p class="text-sm text-gray-300 mb-2">Your short link:</p>
                    <div class="flex items-center justify-between bg-white/10 rounded p-2">
                        <span class="text-accent-blue font-medium">${linkData.short_url}</span>
                        <button onclick="copyToClipboard('${linkData.short_url}')" class="text-gray-400 hover:text-accent-blue">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                
                <div class="flex space-x-2">
                    <button onclick="this.closest('.fixed').remove()" class="flex-1 bg-gray-600 hover:bg-gray-700 py-2 px-4 rounded-lg transition-colors">
                        Close
                    </button>
                    <button onclick="copyToClipboard('${linkData.short_url}'); this.closest('.fixed').remove();" class="flex-1 bg-accent-blue hover:bg-blue-600 py-2 px-4 rounded-lg transition-colors">
                        Copy & Close
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close on backdrop click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// Copy to clipboard functionality
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copied to clipboard!', 'success');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Copied to clipboard!', 'success');
    });
}

// Generate QR Code functionality
function generateQRCode(linkId, linkCode) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/50';
    modal.innerHTML = `
        <div class="glass-effect rounded-xl p-6 m-4 max-w-md w-full">
            <div class="text-center">
                <h3 class="text-xl font-semibold mb-4">Generate QR Code</h3>
                
                <div class="space-y-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Size</label>
                        <select id="qr-size" class="w-full px-4 py-2 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none">
                            <option value="200">Small (200x200)</option>
                            <option value="400" selected>Medium (400x400)</option>
                            <option value="800">Large (800x800)</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Format</label>
                        <select id="qr-format" class="w-full px-4 py-2 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none">
                            <option value="png">PNG</option>
                            <option value="jpg">JPG</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex space-x-2">
                    <button onclick="this.closest('.fixed').remove()" class="flex-1 bg-gray-600 hover:bg-gray-700 py-2 px-4 rounded-lg transition-colors">
                        Cancel
                    </button>
                    <button onclick="processQRGeneration(${linkId}, '${linkCode}')" class="flex-1 bg-accent-blue hover:bg-blue-600 py-2 px-4 rounded-lg transition-colors">
                        Generate
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Process QR code generation
async function processQRGeneration(linkId, linkCode) {
    const size = document.getElementById('qr-size').value;
    const format = document.getElementById('qr-format').value;
    const modal = document.querySelector('.fixed');
    
    try {
        const response = await fetch('/bronze-plan/generate-qr', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                link_id: linkId,
                size: parseInt(size),
                format: format
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification(data.message, 'success');
            modal.remove();
            
            // Show QR code preview
            showQRPreview(data.data);
        } else {
            showNotification(data.message, 'error');
        }
    } catch (error) {
        showNotification('Failed to generate QR code', 'error');
        console.error('Error:', error);
    }
}

// Show QR code preview
function showQRPreview(qrData) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/50';
    modal.innerHTML = `
        <div class="glass-effect rounded-xl p-6 m-4 max-w-md w-full">
            <div class="text-center">
                <h3 class="text-xl font-semibold mb-4">QR Code Generated</h3>
                
                <div class="bg-white p-4 rounded-lg mb-4 inline-block">
                    <img src="${qrData.qr_url}" alt="QR Code" class="w-48 h-48">
                </div>
                
                <p class="text-sm text-gray-300 mb-4">Size: ${qrData.size}x${qrData.size} | Format: ${qrData.format.toUpperCase()}</p>
                
                <div class="flex space-x-2">
                    <button onclick="this.closest('.fixed').remove()" class="flex-1 bg-gray-600 hover:bg-gray-700 py-2 px-4 rounded-lg transition-colors">
                        Close
                    </button>
                    <a href="${qrData.qr_url}" download="qr-code.${qrData.format}" class="flex-1 bg-accent-blue hover:bg-blue-600 py-2 px-4 rounded-lg transition-colors text-center">
                        Download
                    </a>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Delete link functionality
async function deleteLink(linkId, linkCode) {
    if (!confirm(`Are you sure you want to delete the link s4s.plus/${linkCode}?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/bronze-plan/delete-link/${linkId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': csrfToken
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification(data.message, 'success');
            
            // Remove the row from table
            const row = document.querySelector(`[data-link-id="${linkId}"]`);
            if (row) {
                row.remove();
            }
            
            // Refresh stats
            refreshDashboardStats();
        } else {
            showNotification(data.message, 'error');
        }
    } catch (error) {
        showNotification('Failed to delete link', 'error');
        console.error('Error:', error);
    }
}

// Refresh dashboard stats
function refreshDashboardStats() {
    // This would typically reload stats from the server
    // For now, we'll just reload the page if we're on dashboard
    if (window.location.search.includes('page=dashboard') || !window.location.search.includes('page=')) {
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
}

// Show upgrade modal
function showUpgradeModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/50';
    modal.innerHTML = `
        <div class="glass-effect rounded-xl p-6 m-4 max-w-md w-full">
            <div class="text-center">
                <i class="fas fa-crown text-yellow-400 text-4xl mb-4"></i>
                <h3 class="text-xl font-semibold mb-4">Upgrade Required</h3>
                <p class="text-gray-300 mb-6">You've reached your Bronze plan limit. Upgrade to Platinum for unlimited links and advanced features!</p>
                
                <div class="flex space-x-2">
                    <button onclick="this.closest('.fixed').remove()" class="flex-1 bg-gray-600 hover:bg-gray-700 py-2 px-4 rounded-lg transition-colors">
                        Later
                    </button>
                    <button onclick="window.location.href='/#pricing'" class="flex-1 bg-yellow-600 hover:bg-yellow-700 py-2 px-4 rounded-lg transition-colors">
                        Upgrade Now
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Initialize all functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initCreateLinkForm();
    
    // Add click handlers for dynamic elements
    document.addEventListener('click', function(e) {
        // Copy link functionality
        if (e.target.matches('.copy-link-btn')) {
            const link = e.target.getAttribute('data-link');
            copyToClipboard(link);
        }
        
        // QR generation buttons
        if (e.target.matches('.generate-qr-btn')) {
            const linkId = e.target.getAttribute('data-link-id');
            const linkCode = e.target.getAttribute('data-link-code');
            generateQRCode(linkId, linkCode);
        }
        
        // Delete link buttons
        if (e.target.matches('.delete-link-btn')) {
            const linkId = e.target.getAttribute('data-link-id');
            const linkCode = e.target.getAttribute('data-link-code');
            deleteLink(linkId, linkCode);
        }
    });
});
