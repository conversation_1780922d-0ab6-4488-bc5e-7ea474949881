<!-- Settings Page Content -->
<div x-data="settingsManager()">
    <!-- Toastr Container -->
    <div id="settings-toastr-container" class="fixed top-20 right-4 z-50 space-y-2"></div>

    <style>
        .settings-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(30, 41, 59, 0.9) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.4);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .settings-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .form-input {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15);
            outline: none;
            background: rgba(30, 41, 59, 0.9);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        .tab-button {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .tab-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
            transition: left 0.5s;
        }

        .tab-button:hover::before {
            left: 100%;
        }

        .tab-button.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(59, 130, 246, 0.1) 100%);
            border-color: #3b82f6;
            color: #3b82f6;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 56px;
            height: 28px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            transition: 0.4s;
            border-radius: 28px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 3px;
            bottom: 3px;
            background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
            transition: 0.4s;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        input:checked + .toggle-slider {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(28px);
        }

        .loading-spinner {
            border: 3px solid rgba(59, 130, 246, 0.3);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .toastr {
            padding: 16px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(15px);
            transform: translateX(100%);
            animation: slideIn 0.4s ease forwards;
        }

        .toastr.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: 1px solid rgba(16, 185, 129, 0.5);
        }

        .toastr.error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            border: 1px solid rgba(239, 68, 68, 0.5);
        }

        @keyframes slideIn {
            to { transform: translateX(0); }
        }

        .danger-zone {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
            border: 2px solid rgba(239, 68, 68, 0.3);
        }
    </style>

    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-2">
                    Settings
                </h1>
                <p class="text-gray-300">Manage your account preferences and security settings</p>
            </div>
            <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span class="text-sm text-gray-400">Auto-save enabled</span>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="mb-8">
        <div class="flex flex-wrap gap-3 p-2 bg-gray-800/50 rounded-2xl backdrop-blur-sm">
            <button @click="activeTab = 'account'"
                    :class="activeTab === 'account' ? 'active' : ''"
                    class="tab-button px-6 py-3 rounded-xl border-2 border-transparent hover:bg-white/5 transition-all duration-300 font-medium">
                <i class="fas fa-user mr-2"></i>Account
            </button>
            <button @click="activeTab = 'security'"
                    :class="activeTab === 'security' ? 'active' : ''"
                    class="tab-button px-6 py-3 rounded-xl border-2 border-transparent hover:bg-white/5 transition-all duration-300 font-medium">
                <i class="fas fa-shield-alt mr-2"></i>Security
            </button>
            <button @click="activeTab = 'notifications'"
                    :class="activeTab === 'notifications' ? 'active' : ''"
                    class="tab-button px-6 py-3 rounded-xl border-2 border-transparent hover:bg-white/5 transition-all duration-300 font-medium">
                <i class="fas fa-bell mr-2"></i>Notifications
            </button>
            <button @click="activeTab = 'billing'"
                    :class="activeTab === 'billing' ? 'active' : ''"
                    class="tab-button px-6 py-3 rounded-xl border-2 border-transparent hover:bg-white/5 transition-all duration-300 font-medium">
                <i class="fas fa-credit-card mr-2"></i>Billing
            </button>
        </div>
    </div>
                    <!-- Header -->
                    <div class="mb-6 sm:mb-8">
                        <h1 class="text-2xl sm:text-3xl font-bold mb-2">Settings</h1>
                        <p class="text-gray-300 text-sm sm:text-base">Manage your account preferences and security settings</p>
                    </div>

                    <!-- Settings Tabs -->
                    <div class="mb-6">
                        <div class="flex flex-wrap gap-2 border-b border-gray-600">
                            <button @click="activeTab = 'account'" 
                                    :class="activeTab === 'account' ? 'active' : ''"
                                    class="tab-button px-4 py-2 rounded-t-lg border-b-2 border-transparent hover:bg-white/5 transition-colors">
                                <i class="fas fa-user mr-2"></i>Account
                            </button>
                            <button @click="activeTab = 'security'" 
                                    :class="activeTab === 'security' ? 'active' : ''"
                                    class="tab-button px-4 py-2 rounded-t-lg border-b-2 border-transparent hover:bg-white/5 transition-colors">
                                <i class="fas fa-shield-alt mr-2"></i>Security
                            </button>
                            <button @click="activeTab = 'notifications'" 
                                    :class="activeTab === 'notifications' ? 'active' : ''"
                                    class="tab-button px-4 py-2 rounded-t-lg border-b-2 border-transparent hover:bg-white/5 transition-colors">
                                <i class="fas fa-bell mr-2"></i>Notifications
                            </button>
                            <button @click="activeTab = 'billing'" 
                                    :class="activeTab === 'billing' ? 'active' : ''"
                                    class="tab-button px-4 py-2 rounded-t-lg border-b-2 border-transparent hover:bg-white/5 transition-colors">
                                <i class="fas fa-credit-card mr-2"></i>Billing
                            </button>
                        </div>
                    </div>

                    <!-- Account Settings -->
                    <div x-show="activeTab === 'account'" x-transition class="space-y-6">
                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4">Account Information</h2>
                            <form class="space-y-4">
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Username</label>
                                        <input type="text" value="johndoe" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Display Name</label>
                                        <input type="text" value="John Doe" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                                    <input type="email" value="<EMAIL>" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Timezone</label>
                                    <select class="w-full px-4 py-3 rounded-lg form-input text-white">
                                        <option>Asia/Jakarta (UTC+7)</option>
                                        <option>Asia/Singapore (UTC+8)</option>
                                        <option>UTC (UTC+0)</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn-primary py-3 px-6 rounded-lg font-semibold">
                                    <i class="fas fa-save mr-2"></i>Save Changes
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div x-show="activeTab === 'security'" x-transition class="space-y-6">
                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4">Change Password</h2>
                            <form class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Current Password</label>
                                    <input type="password" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">New Password</label>
                                    <input type="password" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Confirm New Password</label>
                                    <input type="password" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                </div>
                                <button type="submit" class="btn-primary py-3 px-6 rounded-lg font-semibold">
                                    <i class="fas fa-key mr-2"></i>Update Password
                                </button>
                            </form>
                        </div>

                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4">Two-Factor Authentication</h2>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium">Enable 2FA</p>
                                    <p class="text-sm text-gray-400">Add an extra layer of security to your account</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div x-show="activeTab === 'notifications'" x-transition class="space-y-6">
                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4">Email Notifications</h2>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium">Link Analytics</p>
                                        <p class="text-sm text-gray-400">Receive weekly analytics reports</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium">Security Alerts</p>
                                        <p class="text-sm text-gray-400">Get notified about account security events</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium">Marketing Updates</p>
                                        <p class="text-sm text-gray-400">Receive news and product updates</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Billing Settings -->
                    <div x-show="activeTab === 'billing'" x-transition class="space-y-6">
                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4">Current Plan</h2>
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <p class="text-lg font-medium">Bronze Plan</p>
                                    <p class="text-sm text-gray-400">100 links • Basic analytics • Standard support</p>
                                </div>
                                <span class="px-3 py-1 bg-yellow-600 text-sm rounded-full">Active</span>
                            </div>
                            <a href="/#pricing" class="btn-primary py-3 px-6 rounded-lg font-semibold inline-block">
                                <i class="fas fa-crown mr-2"></i>Upgrade Plan
                            </a>
                        </div>

                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4 text-red-400">Danger Zone</h2>
                            <div class="space-y-4">
                                <div class="border border-red-500/30 rounded-lg p-4">
                                    <h3 class="font-medium text-red-400 mb-2">Delete Account</h3>
                                    <p class="text-sm text-gray-400 mb-4">Once you delete your account, there is no going back. Please be certain.</p>
                                    <button class="btn-danger py-2 px-4 rounded-lg font-semibold text-sm">
                                        <i class="fas fa-trash mr-2"></i>Delete Account
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
    </div>
</div>
