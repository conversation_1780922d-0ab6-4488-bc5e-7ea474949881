<!DOCTYPE html>
<html lang="id" x-data="{ sidebarOpen: false }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Links - Bronze Plan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-blue': '#0f172a',
                        'glass-blue': 'rgba(30, 58, 138, 0.1)',
                        'accent-blue': '#3b82f6'
                    }
                }
            }
        }
    </script>
    <style>
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(30, 58, 138, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-dark-blue via-slate-900 to-blue-900 text-white min-h-screen">
    
    <!-- Include Navbar (same as dashboard) -->
    <nav class="glass-effect border-b border-blue-500/20 sticky top-0 z-50">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <div class="flex items-center ml-2 lg:ml-0">
                        <img src="https://via.placeholder.com/40x40/3b82f6/ffffff?text=S4S" alt="Sub4Short Plus" class="h-8 w-8 rounded-lg">
                        <span class="ml-3 text-lg font-bold gradient-text">Sub4Short Plus</span>
                        <span class="ml-2 px-2 py-1 bg-yellow-600 text-xs rounded-full">Bronze</span>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <div class="hidden sm:flex items-center space-x-2 text-sm">
                        <i class="fas fa-link text-accent-blue"></i>
                        <span>45/100 Links</span>
                    </div>
                    <div class="relative" x-data="{ userMenuOpen: false }">
                        <button @click="userMenuOpen = !userMenuOpen" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-white/10 transition-colors">
                            <img src="https://via.placeholder.com/32x32/3b82f6/ffffff?text=U" alt="User" class="w-8 h-8 rounded-full">
                            <span class="hidden sm:block">John Doe</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        <div x-show="userMenuOpen" @click.away="userMenuOpen = false" x-transition class="absolute right-0 mt-2 w-48 glass-effect rounded-lg shadow-lg py-2">
                            <a href="#" class="block px-4 py-2 hover:bg-white/10 transition-colors">
                                <i class="fas fa-user mr-2"></i>Profile
                            </a>
                            <a href="#" class="block px-4 py-2 hover:bg-white/10 transition-colors">
                                <i class="fas fa-cog mr-2"></i>Settings
                            </a>
                            <hr class="my-2 border-gray-600">
                            <a href="#" class="block px-4 py-2 hover:bg-white/10 transition-colors text-red-400">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Include Sidebar (same as dashboard) -->
        <aside class="fixed inset-y-0 left-0 z-40 w-64 glass-effect border-r border-blue-500/20 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
               :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'"
               style="top: 64px;">
            <div class="flex flex-col h-full pt-4">
                <nav class="flex-1 px-4 space-y-2">
                    <a href="../dashboard.blade.php" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-home mr-3"></i>
                        Dashboard
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 rounded-lg bg-accent-blue/20 text-accent-blue border border-accent-blue/30">
                        <i class="fas fa-link mr-3"></i>
                        My Links
                    </a>
                    <a href="analytics.blade.php" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-chart-bar mr-3"></i>
                        Analytics
                    </a>
                    <a href="qr-codes.blade.php" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-qrcode mr-3"></i>
                        QR Codes
                    </a>
                    <a href="withdraw.blade.php" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-wallet mr-3"></i>
                        Withdraw
                    </a>
                    
                    <div class="mt-8 p-4 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg border border-blue-500/30">
                        <h4 class="font-semibold mb-2">Upgrade to Platinum</h4>
                        <p class="text-sm text-gray-300 mb-3">Get unlimited links and advanced features</p>
                        <button class="w-full bg-accent-blue hover:bg-blue-600 py-2 px-4 rounded-lg text-sm font-semibold transition-colors">
                            Upgrade Now
                        </button>
                    </div>
                </nav>
            </div>
        </aside>

        <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-30 bg-black/50 lg:hidden" x-transition></div>

        <!-- Main Content -->
        <main class="flex-1 lg:ml-0 min-h-screen">
            <div class="p-6">
                <!-- Header -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
                    <div>
                        <h1 class="text-3xl font-bold mb-2">My Links</h1>
                        <p class="text-gray-300">Manage all your shortened links</p>
                    </div>
                    <button class="mt-4 sm:mt-0 bg-accent-blue hover:bg-blue-600 py-2 px-4 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-plus mr-2"></i>Create New Link
                    </button>
                </div>

                <!-- Stats Summary -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Active Links</p>
                                <p class="text-2xl font-bold">45</p>
                            </div>
                            <i class="fas fa-link text-3xl text-accent-blue"></i>
                        </div>
                    </div>
                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Total Clicks</p>
                                <p class="text-2xl font-bold">1,234</p>
                            </div>
                            <i class="fas fa-mouse-pointer text-3xl text-green-400"></i>
                        </div>
                    </div>
                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Remaining</p>
                                <p class="text-2xl font-bold">55</p>
                            </div>
                            <i class="fas fa-hourglass-half text-3xl text-yellow-400"></i>
                        </div>
                    </div>
                </div>

                <!-- Links Table -->
                <div class="glass-effect rounded-xl overflow-hidden">
                    <div class="p-6 border-b border-blue-500/20">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <h3 class="text-xl font-semibold">Your Links</h3>
                            <div class="mt-4 sm:mt-0 flex space-x-2">
                                <input type="text" placeholder="Search links..." 
                                       class="px-4 py-2 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400">
                                <button class="px-4 py-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-white/5">
                                <tr>
                                    <th class="px-6 py-4 text-left text-sm font-medium text-gray-300">Short Link</th>
                                    <th class="px-6 py-4 text-left text-sm font-medium text-gray-300">Original URL</th>
                                    <th class="px-6 py-4 text-left text-sm font-medium text-gray-300">Clicks</th>
                                    <th class="px-6 py-4 text-left text-sm font-medium text-gray-300">Created</th>
                                    <th class="px-6 py-4 text-left text-sm font-medium text-gray-300">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-blue-500/20">
                                <tr class="hover:bg-white/5 transition-colors">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <span class="text-accent-blue font-medium">s4s.plus/abc123</span>
                                            <button class="ml-2 text-gray-400 hover:text-accent-blue transition-colors">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="text-gray-300 truncate max-w-xs block">https://example.com/very-long-url-here</span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="text-green-400 font-semibold">156</span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="text-gray-400">2 days ago</span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex space-x-2">
                                            <button class="text-accent-blue hover:text-blue-300 transition-colors">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-purple-400 hover:text-purple-300 transition-colors">
                                                <i class="fas fa-qrcode"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300 transition-colors">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <!-- More rows... -->
                                <tr class="hover:bg-white/5 transition-colors">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <span class="text-accent-blue font-medium">s4s.plus/xyz789</span>
                                            <button class="ml-2 text-gray-400 hover:text-accent-blue transition-colors">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="text-gray-300 truncate max-w-xs block">https://another-example.com/page</span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="text-green-400 font-semibold">89</span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="text-gray-400">5 days ago</span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex space-x-2">
                                            <button class="text-accent-blue hover:text-blue-300 transition-colors">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-purple-400 hover:text-purple-300 transition-colors">
                                                <i class="fas fa-qrcode"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300 transition-colors">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="p-6 border-t border-blue-500/20">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-400">Showing 1-10 of 45 links</span>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 bg-white/10 rounded hover:bg-white/20 transition-colors">Previous</button>
                                <button class="px-3 py-1 bg-accent-blue rounded">1</button>
                                <button class="px-3 py-1 bg-white/10 rounded hover:bg-white/20 transition-colors">2</button>
                                <button class="px-3 py-1 bg-white/10 rounded hover:bg-white/20 transition-colors">Next</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

</body>
</html>
