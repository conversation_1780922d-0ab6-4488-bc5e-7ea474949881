<!-- QR Codes Content for Bronze Plan -->
<div class="mb-6 sm:mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl sm:text-3xl font-bold mb-2">QR Codes</h1>
            <p class="text-gray-300 text-sm sm:text-base">Generate and manage QR codes for your links</p>
        </div>
        <button class="mt-4 sm:mt-0 bg-accent-blue hover:bg-blue-600 py-2 px-4 rounded-lg font-semibold transition-colors text-sm sm:text-base">
            <i class="fas fa-qrcode mr-2"></i>Generate QR Code
        </button>
    </div>
</div>

<!-- QR Code Stats -->
<div class="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
    <div class="glass-effect rounded-xl p-4 sm:p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-400 text-xs sm:text-sm">Total QR Codes</p>
                <p class="text-xl sm:text-2xl font-bold">23</p>
            </div>
            <i class="fas fa-qrcode text-2xl sm:text-3xl text-accent-blue"></i>
        </div>
        <p class="text-gray-400 text-xs sm:text-sm mt-2">Generated this month</p>
    </div>

    <div class="glass-effect rounded-xl p-4 sm:p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-400 text-xs sm:text-sm">QR Scans</p>
                <p class="text-xl sm:text-2xl font-bold">456</p>
            </div>
            <i class="fas fa-mobile-alt text-2xl sm:text-3xl text-green-400"></i>
        </div>
        <p class="text-green-400 text-xs sm:text-sm mt-2">
            <i class="fas fa-arrow-up mr-1"></i>+18% this week
        </p>
    </div>

    <div class="glass-effect rounded-xl p-4 sm:p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-400 text-xs sm:text-sm">Most Scanned</p>
                <p class="text-xl sm:text-2xl font-bold">89</p>
            </div>
            <i class="fas fa-trophy text-2xl sm:text-3xl text-yellow-400"></i>
        </div>
        <p class="text-gray-400 text-xs sm:text-sm mt-2">Single QR code</p>
    </div>
</div>

<!-- QR Code Generator -->
<div class="glass-effect rounded-xl p-4 sm:p-6 mb-6 sm:mb-8">
    <h3 class="text-lg sm:text-xl font-semibold mb-4">
        <i class="fas fa-magic text-accent-blue mr-2"></i>
        Generate New QR Code
    </h3>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Select Link</label>
                <select class="w-full px-3 sm:px-4 py-2 sm:py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all text-sm sm:text-base">
                    <option>s4s.plus/abc123 - example.com/page1</option>
                    <option>s4s.plus/xyz789 - example.com/page2</option>
                    <option>s4s.plus/def456 - example.com/page3</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">QR Code Size</label>
                <select class="w-full px-3 sm:px-4 py-2 sm:py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all text-sm sm:text-base">
                    <option>Small (200x200)</option>
                    <option selected>Medium (400x400)</option>
                    <option>Large (800x800)</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Format</label>
                <select class="w-full px-3 sm:px-4 py-2 sm:py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all text-sm sm:text-base">
                    <option>PNG</option>
                    <option>JPG</option>
                    <option>SVG (Platinum only)</option>
                </select>
            </div>
            <button class="w-full bg-accent-blue hover:bg-blue-600 py-2 sm:py-3 px-4 rounded-lg font-semibold transition-colors text-sm sm:text-base">
                <i class="fas fa-qrcode mr-2"></i>Generate QR Code
            </button>
        </div>
        <div class="flex items-center justify-center">
            <div class="w-48 h-48 sm:w-64 sm:h-64 bg-white/10 rounded-lg flex items-center justify-center border-2 border-dashed border-blue-500/30">
                <div class="text-center">
                    <i class="fas fa-qrcode text-4xl text-gray-600 mb-4"></i>
                    <p class="text-gray-400 text-sm">QR Code Preview</p>
                    <p class="text-gray-500 text-xs mt-2">Select a link to generate</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- QR Codes List -->
<div class="glass-effect rounded-xl overflow-hidden">
    <div class="p-4 sm:p-6 border-b border-blue-500/20">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h3 class="text-lg sm:text-xl font-semibold">Your QR Codes</h3>
            <div class="mt-4 sm:mt-0 flex space-x-2">
                <input type="text" placeholder="Search QR codes..." 
                       class="px-3 sm:px-4 py-2 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400 text-sm">
                <button class="px-3 sm:px-4 py-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-white/5">
                <tr>
                    <th class="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs sm:text-sm font-medium text-gray-300">QR Code</th>
                    <th class="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs sm:text-sm font-medium text-gray-300">Link</th>
                    <th class="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs sm:text-sm font-medium text-gray-300">Scans</th>
                    <th class="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs sm:text-sm font-medium text-gray-300 hidden md:table-cell">Created</th>
                    <th class="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs sm:text-sm font-medium text-gray-300">Actions</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-blue-500/20">
                <tr class="hover:bg-white/5 transition-colors">
                    <td class="px-4 sm:px-6 py-3 sm:py-4">
                        <div class="w-12 h-12 bg-white rounded border-2 flex items-center justify-center">
                            <i class="fas fa-qrcode text-black text-lg"></i>
                        </div>
                    </td>
                    <td class="px-4 sm:px-6 py-3 sm:py-4">
                        <div>
                            <p class="text-accent-blue font-medium text-xs sm:text-sm">s4s.plus/abc123</p>
                            <p class="text-gray-400 text-xs truncate max-w-xs">https://example.com/very-long-url-here</p>
                        </div>
                    </td>
                    <td class="px-4 sm:px-6 py-3 sm:py-4">
                        <span class="text-green-400 font-semibold text-xs sm:text-sm">89</span>
                    </td>
                    <td class="px-4 sm:px-6 py-3 sm:py-4 hidden md:table-cell">
                        <span class="text-gray-400 text-xs sm:text-sm">2 days ago</span>
                    </td>
                    <td class="px-4 sm:px-6 py-3 sm:py-4">
                        <div class="flex space-x-1 sm:space-x-2">
                            <button class="text-accent-blue hover:text-blue-300 transition-colors" title="Download">
                                <i class="fas fa-download text-xs sm:text-sm"></i>
                            </button>
                            <button class="text-green-400 hover:text-green-300 transition-colors" title="View">
                                <i class="fas fa-eye text-xs sm:text-sm"></i>
                            </button>
                            <button class="text-purple-400 hover:text-purple-300 transition-colors" title="Analytics">
                                <i class="fas fa-chart-line text-xs sm:text-sm"></i>
                            </button>
                            <button class="text-red-400 hover:text-red-300 transition-colors" title="Delete">
                                <i class="fas fa-trash text-xs sm:text-sm"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="hover:bg-white/5 transition-colors">
                    <td class="px-4 sm:px-6 py-3 sm:py-4">
                        <div class="w-12 h-12 bg-white rounded border-2 flex items-center justify-center">
                            <i class="fas fa-qrcode text-black text-lg"></i>
                        </div>
                    </td>
                    <td class="px-4 sm:px-6 py-3 sm:py-4">
                        <div>
                            <p class="text-accent-blue font-medium text-xs sm:text-sm">s4s.plus/xyz789</p>
                            <p class="text-gray-400 text-xs truncate max-w-xs">https://example.com/another-page</p>
                        </div>
                    </td>
                    <td class="px-4 sm:px-6 py-3 sm:py-4">
                        <span class="text-green-400 font-semibold text-xs sm:text-sm">67</span>
                    </td>
                    <td class="px-4 sm:px-6 py-3 sm:py-4 hidden md:table-cell">
                        <span class="text-gray-400 text-xs sm:text-sm">5 days ago</span>
                    </td>
                    <td class="px-4 sm:px-6 py-3 sm:py-4">
                        <div class="flex space-x-1 sm:space-x-2">
                            <button class="text-accent-blue hover:text-blue-300 transition-colors" title="Download">
                                <i class="fas fa-download text-xs sm:text-sm"></i>
                            </button>
                            <button class="text-green-400 hover:text-green-300 transition-colors" title="View">
                                <i class="fas fa-eye text-xs sm:text-sm"></i>
                            </button>
                            <button class="text-purple-400 hover:text-purple-300 transition-colors" title="Analytics">
                                <i class="fas fa-chart-line text-xs sm:text-sm"></i>
                            </button>
                            <button class="text-red-400 hover:text-red-300 transition-colors" title="Delete">
                                <i class="fas fa-trash text-xs sm:text-sm"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Bronze Limitations -->
<div class="glass-effect rounded-xl p-4 sm:p-6 border-l-4 border-yellow-500 mt-6 sm:mt-8">
    <div class="flex items-start">
        <i class="fas fa-info-circle text-yellow-400 mr-3 mt-1"></i>
        <div>
            <h4 class="font-semibold text-yellow-400 mb-2 text-sm sm:text-base">Bronze Plan QR Code Limitations</h4>
            <ul class="text-xs sm:text-sm text-gray-300 space-y-1 mb-4">
                <li>• Basic QR code generation only</li>
                <li>• PNG and JPG formats only</li>
                <li>• Standard sizes available</li>
                <li>• No custom branding/logo</li>
                <li>• No bulk generation</li>
            </ul>
            <button class="bg-yellow-600 hover:bg-yellow-700 py-2 px-4 rounded-lg text-xs sm:text-sm font-semibold transition-colors">
                Upgrade for Advanced QR Features
            </button>
        </div>
    </div>
</div>
