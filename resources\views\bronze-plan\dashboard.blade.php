<!DOCTYPE html>
<html lang="id" x-data="{ sidebarOpen: false }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Bronze - Sub4Short Plus</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-blue': '#0f172a',
                        'glass-blue': 'rgba(30, 58, 138, 0.1)',
                        'accent-blue': '#3b82f6'
                    }
                }
            }
        }
    </script>
    <style>
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(30, 58, 138, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-dark-blue via-slate-900 to-blue-900 text-white min-h-screen">
    
    <!-- Navbar -->
    <nav class="glass-effect border-b border-blue-500/20 sticky top-0 z-50">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Left: Logo & Mobile Menu Button -->
                <div class="flex items-center">
                    <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <div class="flex items-center ml-2 lg:ml-0">
                        <img src="https://via.placeholder.com/40x40/3b82f6/ffffff?text=S4S" alt="Sub4Short Plus" class="h-8 w-8 rounded-lg">
                        <span class="ml-3 text-lg font-bold gradient-text">Sub4Short Plus</span>
                        <span class="ml-2 px-2 py-1 bg-yellow-600 text-xs rounded-full">Bronze</span>
                    </div>
                </div>
                
                <!-- Right: User Menu -->
                <div class="flex items-center space-x-4">
                    <div class="hidden sm:flex items-center space-x-2 text-sm">
                        <i class="fas fa-link text-accent-blue"></i>
                        <span>45/100 Links</span>
                    </div>
                    <div class="relative" x-data="{ userMenuOpen: false }">
                        <button @click="userMenuOpen = !userMenuOpen" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-white/10 transition-colors">
                            <img src="https://via.placeholder.com/32x32/3b82f6/ffffff?text=U" alt="User" class="w-8 h-8 rounded-full">
                            <span class="hidden sm:block">John Doe</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        <div x-show="userMenuOpen" @click.away="userMenuOpen = false" x-transition class="absolute right-0 mt-2 w-48 glass-effect rounded-lg shadow-lg py-2">
                            <a href="#" class="block px-4 py-2 hover:bg-white/10 transition-colors">
                                <i class="fas fa-user mr-2"></i>Profile
                            </a>
                            <a href="#" class="block px-4 py-2 hover:bg-white/10 transition-colors">
                                <i class="fas fa-cog mr-2"></i>Settings
                            </a>
                            <hr class="my-2 border-gray-600">
                            <a href="#" class="block px-4 py-2 hover:bg-white/10 transition-colors text-red-400">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="fixed inset-y-0 left-0 z-40 w-64 glass-effect border-r border-blue-500/20 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
               :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'"
               style="top: 64px;">
            <div class="flex flex-col h-full pt-4">
                <!-- Navigation Menu -->
                <nav class="flex-1 px-4 space-y-2">
                    <a href="#" class="flex items-center px-4 py-3 rounded-lg bg-accent-blue/20 text-accent-blue border border-accent-blue/30">
                        <i class="fas fa-home mr-3"></i>
                        Dashboard
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-link mr-3"></i>
                        My Links
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-chart-bar mr-3"></i>
                        Analytics
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-qrcode mr-3"></i>
                        QR Codes
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-wallet mr-3"></i>
                        Withdraw
                    </a>
                    
                    <!-- Upgrade Section -->
                    <div class="mt-8 p-4 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg border border-blue-500/30">
                        <h4 class="font-semibold mb-2">Upgrade to Platinum</h4>
                        <p class="text-sm text-gray-300 mb-3">Get unlimited links and advanced features</p>
                        <button class="w-full bg-accent-blue hover:bg-blue-600 py-2 px-4 rounded-lg text-sm font-semibold transition-colors">
                            Upgrade Now
                        </button>
                    </div>
                </nav>
            </div>
        </aside>

        <!-- Overlay for mobile -->
        <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-30 bg-black/50 lg:hidden" x-transition></div>

        <!-- Main Content -->
        <main class="flex-1 lg:ml-0 min-h-screen">
            <div class="p-6">
                <!-- Welcome Section -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold mb-2">Welcome back, John! 👋</h1>
                    <p class="text-gray-300">Here's what's happening with your Bronze account</p>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Total Links</p>
                                <p class="text-2xl font-bold">45</p>
                            </div>
                            <i class="fas fa-link text-3xl text-accent-blue"></i>
                        </div>
                        <div class="mt-4">
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-accent-blue h-2 rounded-full" style="width: 45%"></div>
                            </div>
                            <p class="text-xs text-gray-400 mt-1">45/100 used</p>
                        </div>
                    </div>

                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Total Clicks</p>
                                <p class="text-2xl font-bold">1,234</p>
                            </div>
                            <i class="fas fa-mouse-pointer text-3xl text-green-400"></i>
                        </div>
                        <p class="text-green-400 text-sm mt-2">
                            <i class="fas fa-arrow-up mr-1"></i>+12% from last month
                        </p>
                    </div>

                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">QR Codes</p>
                                <p class="text-2xl font-bold">23</p>
                            </div>
                            <i class="fas fa-qrcode text-3xl text-purple-400"></i>
                        </div>
                        <p class="text-gray-400 text-sm mt-2">Generated this month</p>
                    </div>

                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Earnings</p>
                                <p class="text-2xl font-bold">Rp 0</p>
                            </div>
                            <i class="fas fa-coins text-3xl text-yellow-400"></i>
                        </div>
                        <p class="text-gray-400 text-sm mt-2">Available to withdraw</p>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Create Short Link -->
                    <div class="glass-effect rounded-xl p-6">
                        <h3 class="text-xl font-semibold mb-4">
                            <i class="fas fa-plus-circle text-accent-blue mr-2"></i>
                            Create Short Link
                        </h3>
                        <div class="space-y-4">
                            <input type="url" placeholder="Enter your long URL here..." 
                                   class="w-full px-4 py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400">
                            <button class="w-full bg-accent-blue hover:bg-blue-600 py-3 px-4 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-magic mr-2"></i>Shorten URL
                            </button>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="glass-effect rounded-xl p-6">
                        <h3 class="text-xl font-semibold mb-4">
                            <i class="fas fa-clock text-accent-blue mr-2"></i>
                            Recent Activity
                        </h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between py-2">
                                <div class="flex items-center">
                                    <i class="fas fa-link text-green-400 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium">New link created</p>
                                        <p class="text-xs text-gray-400">2 hours ago</p>
                                    </div>
                                </div>
                                <span class="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full">Success</span>
                            </div>
                            <div class="flex items-center justify-between py-2">
                                <div class="flex items-center">
                                    <i class="fas fa-qrcode text-purple-400 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium">QR code generated</p>
                                        <p class="text-xs text-gray-400">5 hours ago</p>
                                    </div>
                                </div>
                                <span class="text-xs bg-purple-500/20 text-purple-400 px-2 py-1 rounded-full">Generated</span>
                            </div>
                            <div class="flex items-center justify-between py-2">
                                <div class="flex items-center">
                                    <i class="fas fa-mouse-pointer text-blue-400 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium">Link clicked 15 times</p>
                                        <p class="text-xs text-gray-400">1 day ago</p>
                                    </div>
                                </div>
                                <span class="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">Activity</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bronze Plan Limitations Notice -->
                <div class="glass-effect rounded-xl p-6 border-l-4 border-yellow-500">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-yellow-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-semibold text-yellow-400 mb-2">Bronze Plan Limitations</h4>
                            <ul class="text-sm text-gray-300 space-y-1">
                                <li>• Maximum 100 links per month</li>
                                <li>• Basic analytics only</li>
                                <li>• QR code generation included</li>
                                <li>• No custom domain support</li>
                                <li>• Standard support</li>
                            </ul>
                            <button class="mt-4 bg-yellow-600 hover:bg-yellow-700 py-2 px-4 rounded-lg text-sm font-semibold transition-colors">
                                Upgrade to Platinum
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

</body>
</html>
