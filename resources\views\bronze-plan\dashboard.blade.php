<!DOCTYPE html>
<html lang="id" x-data="{ sidebarOpen: false, currentPage: '{{ request()->get('page', 'dashboard') }}' }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Bronze - Sub4Short Plus</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-blue': '#0f172a',
                        'glass-blue': 'rgba(30, 58, 138, 0.1)',
                        'accent-blue': '#3b82f6'
                    }
                }
            }
        }
    </script>
    <style>
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(30, 58, 138, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        @media (max-width: 1024px) {
            .sidebar-overlay {
                backdrop-filter: blur(5px);
            }
        }
        @media (max-width: 640px) {
            .xs\:block { display: block; }
        }

        /* Dropdown animations */
        .dropdown-enter {
            opacity: 0;
            transform: translateY(10px);
        }
        .dropdown-enter-active {
            opacity: 1;
            transform: translateY(0);
            transition: all 0.2s ease-out;
        }
        .dropdown-leave {
            opacity: 1;
            transform: translateY(0);
        }
        .dropdown-leave-active {
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.2s ease-in;
        }

        /* Notification badge pulse */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .notification-badge {
            animation: pulse 2s infinite;
        }

        /* Sidebar full height fix */
        .sidebar-full {
            height: 100vh !important;
            top: 0 !important;
        }

        /* Scrollbar styling for notifications */
        .notification-scroll::-webkit-scrollbar {
            width: 4px;
        }
        .notification-scroll::-webkit-scrollbar-track {
            background: rgba(59, 130, 246, 0.1);
            border-radius: 2px;
        }
        .notification-scroll::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.3);
            border-radius: 2px;
        }
        .notification-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.5);
        }

        /* Main content scrollable area */
        .main-content-scroll {
            height: calc(100vh - 64px);
            overflow-y: auto;
            overflow-x: hidden;
            width: 100%;
        }

        .main-content-scroll::-webkit-scrollbar {
            width: 6px;
        }
        .main-content-scroll::-webkit-scrollbar-track {
            background: rgba(59, 130, 246, 0.1);
            border-radius: 3px;
        }
        .main-content-scroll::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.3);
            border-radius: 3px;
        }
        .main-content-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.5);
        }

        /* Layout fixes */
        .layout-container {
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        .navbar-fixed {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            height: 64px;
        }

        .sidebar-fixed {
            position: fixed;
            top: 64px;
            left: 0;
            bottom: 0;
            z-index: 40;
            height: calc(100vh - 64px);
        }

        .content-area {
            margin-top: 64px;
            margin-left: 0;
            width: 100%;
        }

        @media (min-width: 1024px) {
            .content-area {
                margin-left: 16rem; /* 256px / 16 = 16rem */
                width: calc(100% - 16rem);
            }
        }

        @media (max-width: 1023px) {
            .sidebar-fixed {
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }
            .sidebar-fixed.show {
                transform: translateX(0);
            }
            .content-area {
                margin-left: 0 !important;
                width: 100% !important;
            }
        }

        /* Smooth scrolling */
        .main-content-scroll {
            scroll-behavior: smooth;
        }

        /* Hide scrollbar on mobile for cleaner look */
        @media (max-width: 768px) {
            .main-content-scroll::-webkit-scrollbar {
                width: 0px;
                background: transparent;
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-dark-blue via-slate-900 to-blue-900 text-white overflow-x-hidden">
    <div class="layout-container">
        <!-- Navbar -->
        <nav class="navbar-fixed glass-effect border-b border-blue-500/20">
            <div class="px-3 sm:px-4 lg:px-6">
                <div class="flex justify-between items-center h-16">
                <!-- Left: Logo & Mobile Menu Button -->
                <div class="flex items-center">
                    <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors mr-1">
                        <i class="fas fa-bars text-lg sm:text-xl"></i>
                    </button>
                    <div class="flex items-center">
                        <img src="https://via.placeholder.com/40x40/3b82f6/ffffff?text=S4S" alt="Sub4Short Plus" class="h-7 w-7 sm:h-8 sm:w-8 rounded-lg">
                        <span class="ml-2 sm:ml-3 text-base sm:text-lg font-bold gradient-text hidden xs:block">Sub4Short Plus</span>
                        <span class="ml-1 sm:ml-2 px-1.5 sm:px-2 py-0.5 sm:py-1 bg-yellow-600 text-xs rounded-full">Bronze</span>
                    </div>
                </div>

                <!-- Right: User Menu -->
                <div class="flex items-center space-x-2 sm:space-x-4">
                    <div class="hidden sm:flex items-center space-x-2 text-sm">
                        <i class="fas fa-link text-accent-blue"></i>
                        <span class="hidden md:inline">45/100 Links</span>
                        <span class="md:hidden">45/100</span>
                    </div>

                    <!-- Notifications -->
                    <div class="relative" x-data="{ notifOpen: false }">
                        <button @click="notifOpen = !notifOpen" class="relative p-2 rounded-lg hover:bg-white/10 transition-colors">
                            <i class="fas fa-bell text-lg sm:text-xl"></i>
                            <!-- Notification Badge -->
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center notification-badge">3</span>
                        </button>
                        <div x-show="notifOpen" @click.away="notifOpen = false" x-transition
                             class="absolute right-0 top-full mt-2 w-72 sm:w-80 bg-slate-800 border border-blue-500/30 rounded-lg shadow-xl py-2 z-50 max-h-96 overflow-y-auto notification-scroll">
                            <div class="px-4 py-2 border-b border-blue-500/20">
                                <h3 class="font-semibold text-sm">Notifications</h3>
                            </div>
                            <div class="py-2">
                                <div class="px-4 py-3 hover:bg-slate-700 transition-colors border-l-4 border-green-500">
                                    <div class="flex items-start">
                                        <i class="fas fa-check-circle text-green-400 mr-3 mt-1"></i>
                                        <div class="flex-1">
                                            <p class="text-sm font-medium">Link Created Successfully</p>
                                            <p class="text-xs text-gray-400 mt-1">Your new short link s4s.plus/abc123 is ready</p>
                                            <p class="text-xs text-gray-500 mt-1">2 hours ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-4 py-3 hover:bg-slate-700 transition-colors border-l-4 border-blue-500">
                                    <div class="flex items-start">
                                        <i class="fas fa-chart-line text-blue-400 mr-3 mt-1"></i>
                                        <div class="flex-1">
                                            <p class="text-sm font-medium">High Traffic Alert</p>
                                            <p class="text-xs text-gray-400 mt-1">Your link received 50+ clicks in the last hour</p>
                                            <p class="text-xs text-gray-500 mt-1">5 hours ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-4 py-3 hover:bg-slate-700 transition-colors border-l-4 border-yellow-500">
                                    <div class="flex items-start">
                                        <i class="fas fa-exclamation-triangle text-yellow-400 mr-3 mt-1"></i>
                                        <div class="flex-1">
                                            <p class="text-sm font-medium">Plan Limit Warning</p>
                                            <p class="text-xs text-gray-400 mt-1">You've used 45/100 links. Consider upgrading.</p>
                                            <p class="text-xs text-gray-500 mt-1">1 day ago</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="px-4 py-2 border-t border-blue-500/20">
                                <a href="#" class="text-xs text-accent-blue hover:text-blue-300 transition-colors">View all notifications</a>
                            </div>
                        </div>
                    </div>

                    <!-- User Profile Dropdown -->
                    <div class="relative" x-data="{ userMenuOpen: false }">
                        <button @click="userMenuOpen = !userMenuOpen" class="flex items-center space-x-1 sm:space-x-2 p-1.5 sm:p-2 rounded-lg hover:bg-white/10 transition-colors">
                            <img id="navbar-avatar" src="https://via.placeholder.com/32x32/3b82f6/ffffff?text=U" alt="User" class="navbar-avatar user-avatar w-7 h-7 sm:w-8 sm:h-8 rounded-full object-cover">
                            <span class="hidden sm:block text-sm">John Doe</span>
                            <i class="fas fa-chevron-down text-xs sm:text-sm" :class="userMenuOpen ? 'rotate-180' : ''"></i>
                        </button>
                        <div x-show="userMenuOpen" @click.away="userMenuOpen = false" x-transition
                             class="absolute right-0 top-full mt-2 w-44 sm:w-48 bg-slate-800 border border-blue-500/30 rounded-lg shadow-xl py-2 z-50">
                            <div class="px-3 sm:px-4 py-2 border-b border-blue-500/20">
                                <p class="text-sm font-medium">John Doe</p>
                                <p class="text-xs text-gray-400"><EMAIL></p>
                                <span class="inline-block mt-1 px-2 py-1 bg-yellow-600 text-xs rounded-full">Bronze Plan</span>
                            </div>
                            <a href="{{ route('bronze.dashboard', ['page' => 'profile']) }}" class="block px-3 sm:px-4 py-2 hover:bg-slate-700 transition-colors text-sm">
                                <i class="fas fa-user mr-2"></i>Profile
                            </a>
                            <a href="{{ route('bronze.dashboard', ['page' => 'settings']) }}" class="block px-3 sm:px-4 py-2 hover:bg-slate-700 transition-colors text-sm">
                                <i class="fas fa-cog mr-2"></i>Settings
                            </a>
                            <a href="#" class="block px-3 sm:px-4 py-2 hover:bg-slate-700 transition-colors text-sm">
                                <i class="fas fa-crown mr-2 text-yellow-400"></i>Upgrade Plan
                            </a>
                            <hr class="my-2 border-gray-600">
                            <a href="#" class="block px-3 sm:px-4 py-2 hover:bg-red-600/20 transition-colors text-red-400 text-sm">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="sidebar-fixed w-56 sm:w-64 glass-effect border-r border-blue-500/20 transform transition-transform duration-300 ease-in-out lg:translate-x-0"
               :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'">
            <div class="flex flex-col h-full pt-3 sm:pt-4 pb-4">
                <!-- Navigation Menu -->
                <nav class="flex-1 px-3 sm:px-4 space-y-1 sm:space-y-2">
                    <a href="?page=dashboard"
                       :class="currentPage === 'dashboard' ? 'bg-accent-blue/20 text-accent-blue border border-accent-blue/30' : 'hover:bg-white/10'"
                       class="flex items-center px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg transition-colors text-sm sm:text-base">
                        <i class="fas fa-home mr-2 sm:mr-3 text-sm sm:text-base"></i>
                        Dashboard
                    </a>
                    <a href="?page=my-links"
                       :class="currentPage === 'my-links' ? 'bg-accent-blue/20 text-accent-blue border border-accent-blue/30' : 'hover:bg-white/10'"
                       class="flex items-center px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg transition-colors text-sm sm:text-base">
                        <i class="fas fa-link mr-2 sm:mr-3 text-sm sm:text-base"></i>
                        My Links
                    </a>
                    <a href="?page=analytics"
                       :class="currentPage === 'analytics' ? 'bg-accent-blue/20 text-accent-blue border border-accent-blue/30' : 'hover:bg-white/10'"
                       class="flex items-center px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg transition-colors text-sm sm:text-base">
                        <i class="fas fa-chart-bar mr-2 sm:mr-3 text-sm sm:text-base"></i>
                        Analytics
                    </a>
                    <a href="?page=qr-codes"
                       :class="currentPage === 'qr-codes' ? 'bg-accent-blue/20 text-accent-blue border border-accent-blue/30' : 'hover:bg-white/10'"
                       class="flex items-center px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg transition-colors text-sm sm:text-base">
                        <i class="fas fa-qrcode mr-2 sm:mr-3 text-sm sm:text-base"></i>
                        QR Codes
                    </a>
                    <a href="?page=withdraw"
                       :class="currentPage === 'withdraw' ? 'bg-accent-blue/20 text-accent-blue border border-accent-blue/30' : 'hover:bg-white/10'"
                       class="flex items-center px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg transition-colors text-sm sm:text-base">
                        <i class="fas fa-wallet mr-2 sm:mr-3 text-sm sm:text-base"></i>
                        Withdraw
                    </a>

                    <!-- Upgrade Section -->
                    <div class="mt-6 sm:mt-8 p-3 sm:p-4 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg border border-blue-500/30">
                        <h4 class="font-semibold mb-2 text-sm sm:text-base">Upgrade to Platinum</h4>
                        <p class="text-xs sm:text-sm text-gray-300 mb-3">Get unlimited links and advanced features</p>
                        <button class="w-full bg-accent-blue hover:bg-blue-600 py-1.5 sm:py-2 px-3 sm:px-4 rounded-lg text-xs sm:text-sm font-semibold transition-colors">
                            Upgrade Now
                        </button>
                    </div>
                </nav>
            </div>
        </aside>

        <!-- Overlay for mobile -->
        <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-30 bg-black/50 sidebar-overlay lg:hidden" x-transition style="top: 64px;"></div>

        <!-- Main Content -->
        <main class="content-area">
            <div class="main-content-scroll">
                <div class="p-3 sm:p-4 lg:p-6">

                @php
                    $page = request()->get('page', 'dashboard');
                @endphp

                @if($page === 'dashboard')
                    <!-- Dashboard Content -->
                    <div class="mb-6 sm:mb-8">
                        <h1 class="text-2xl sm:text-3xl font-bold mb-2">Welcome back, John! 👋</h1>
                        <p class="text-gray-300 text-sm sm:text-base">Here's what's happening with your Bronze account</p>
                    </div>

                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-400 text-xs sm:text-sm">Total Links</p>
                                    <p class="text-xl sm:text-2xl font-bold">45</p>
                                </div>
                                <i class="fas fa-link text-2xl sm:text-3xl text-accent-blue"></i>
                            </div>
                            <div class="mt-3 sm:mt-4">
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="bg-accent-blue h-2 rounded-full" style="width: 45%"></div>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">45/100 used</p>
                            </div>
                        </div>

                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-400 text-xs sm:text-sm">Total Clicks</p>
                                    <p class="text-xl sm:text-2xl font-bold">1,234</p>
                                </div>
                                <i class="fas fa-mouse-pointer text-2xl sm:text-3xl text-green-400"></i>
                            </div>
                            <p class="text-green-400 text-xs sm:text-sm mt-2">
                                <i class="fas fa-arrow-up mr-1"></i>+12% from last month
                            </p>
                        </div>

                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-400 text-xs sm:text-sm">QR Codes</p>
                                    <p class="text-xl sm:text-2xl font-bold">23</p>
                                </div>
                                <i class="fas fa-qrcode text-2xl sm:text-3xl text-purple-400"></i>
                            </div>
                            <p class="text-gray-400 text-xs sm:text-sm mt-2">Generated this month</p>
                        </div>

                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-400 text-xs sm:text-sm">Earnings</p>
                                    <p class="text-xl sm:text-2xl font-bold">Rp 0</p>
                                </div>
                                <i class="fas fa-coins text-2xl sm:text-3xl text-yellow-400"></i>
                            </div>
                            <p class="text-gray-400 text-xs sm:text-sm mt-2">Available to withdraw</p>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-6 sm:mb-8">
                        <!-- Create Short Link -->
                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <h3 class="text-lg sm:text-xl font-semibold mb-4">
                                <i class="fas fa-plus-circle text-accent-blue mr-2"></i>
                                Create Short Link
                            </h3>
                            <form id="create-link-form" class="space-y-4">
                                <input type="url" id="url-input" placeholder="Enter your long URL here..."
                                       class="w-full px-3 sm:px-4 py-2 sm:py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400 text-sm sm:text-base" required>
                                <button type="submit" id="create-link-btn" class="w-full bg-accent-blue hover:bg-blue-600 py-2 sm:py-3 px-4 rounded-lg font-semibold transition-colors text-sm sm:text-base">
                                    <i class="fas fa-magic mr-2"></i>Shorten URL
                                </button>
                            </form>
                        </div>

                        <!-- Recent Activity -->
                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <h3 class="text-lg sm:text-xl font-semibold mb-4">
                                <i class="fas fa-clock text-accent-blue mr-2"></i>
                                Recent Activity
                            </h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between py-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-link text-green-400 mr-2 sm:mr-3 text-sm sm:text-base"></i>
                                        <div>
                                            <p class="text-xs sm:text-sm font-medium">New link created</p>
                                            <p class="text-xs text-gray-400">2 hours ago</p>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full">Success</span>
                                </div>
                                <div class="flex items-center justify-between py-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-qrcode text-purple-400 mr-2 sm:mr-3 text-sm sm:text-base"></i>
                                        <div>
                                            <p class="text-xs sm:text-sm font-medium">QR code generated</p>
                                            <p class="text-xs text-gray-400">5 hours ago</p>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-purple-500/20 text-purple-400 px-2 py-1 rounded-full">Generated</span>
                                </div>
                                <div class="flex items-center justify-between py-2">
                                    <div class="flex items-center">
                                        <i class="fas fa-mouse-pointer text-blue-400 mr-2 sm:mr-3 text-sm sm:text-base"></i>
                                        <div>
                                            <p class="text-xs sm:text-sm font-medium">Link clicked 15 times</p>
                                            <p class="text-xs text-gray-400">1 day ago</p>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">Activity</span>
                                </div>
                            </div>
                        </div>
                </div>

                    <!-- Bronze Plan Limitations Notice -->
                    <div class="glass-effect rounded-xl p-4 sm:p-6 border-l-4 border-yellow-500">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-yellow-400 mr-3 mt-1"></i>
                            <div>
                                <h4 class="font-semibold text-yellow-400 mb-2 text-sm sm:text-base">Bronze Plan Limitations</h4>
                                <ul class="text-xs sm:text-sm text-gray-300 space-y-1">
                                    <li>• Maximum 100 links per month</li>
                                    <li>• Basic analytics only</li>
                                    <li>• QR code generation included</li>
                                    <li>• No custom domain support</li>
                                    <li>• Standard support</li>
                                </ul>
                                <button class="mt-4 bg-yellow-600 hover:bg-yellow-700 py-2 px-4 rounded-lg text-xs sm:text-sm font-semibold transition-colors">
                                    Upgrade to Platinum
                                </button>
                            </div>
                        </div>
                    </div>

                @elseif($page === 'my-links')
                    <!-- My Links Content -->
                    <div class="mb-6 sm:mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl sm:text-3xl font-bold mb-2">My Links</h1>
                                <p class="text-gray-300 text-sm sm:text-base">Manage all your shortened links</p>
                            </div>
                            <button class="mt-4 sm:mt-0 bg-accent-blue hover:bg-blue-600 py-2 px-4 rounded-lg font-semibold transition-colors text-sm sm:text-base">
                                <i class="fas fa-plus mr-2"></i>Create New Link
                            </button>
                        </div>
                    </div>

                    <!-- Stats Summary -->
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-400 text-xs sm:text-sm">Active Links</p>
                                    <p class="text-xl sm:text-2xl font-bold">45</p>
                                </div>
                                <i class="fas fa-link text-2xl sm:text-3xl text-accent-blue"></i>
                            </div>
                        </div>
                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-400 text-xs sm:text-sm">Total Clicks</p>
                                    <p class="text-xl sm:text-2xl font-bold">1,234</p>
                                </div>
                                <i class="fas fa-mouse-pointer text-2xl sm:text-3xl text-green-400"></i>
                            </div>
                        </div>
                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-400 text-xs sm:text-sm">Remaining</p>
                                    <p class="text-xl sm:text-2xl font-bold">55</p>
                                </div>
                                <i class="fas fa-hourglass-half text-2xl sm:text-3xl text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Links Table -->
                    <div class="glass-effect rounded-xl overflow-hidden">
                        <div class="p-4 sm:p-6 border-b border-blue-500/20">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                <h3 class="text-lg sm:text-xl font-semibold">Your Links</h3>
                                <div class="mt-4 sm:mt-0 flex space-x-2">
                                    <input type="text" placeholder="Search links..."
                                           class="px-3 sm:px-4 py-2 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400 text-sm">
                                    <button class="px-3 sm:px-4 py-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-white/5">
                                    <tr>
                                        <th class="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs sm:text-sm font-medium text-gray-300">Short Link</th>
                                        <th class="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs sm:text-sm font-medium text-gray-300 hidden sm:table-cell">Original URL</th>
                                        <th class="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs sm:text-sm font-medium text-gray-300">Clicks</th>
                                        <th class="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs sm:text-sm font-medium text-gray-300 hidden md:table-cell">Created</th>
                                        <th class="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs sm:text-sm font-medium text-gray-300">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-blue-500/20">
                                    <tr class="hover:bg-white/5 transition-colors">
                                        <td class="px-4 sm:px-6 py-3 sm:py-4">
                                            <div class="flex items-center">
                                                <span class="text-accent-blue font-medium text-xs sm:text-sm">s4s.plus/abc123</span>
                                                <button class="ml-2 text-gray-400 hover:text-accent-blue transition-colors">
                                                    <i class="fas fa-copy text-xs"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td class="px-4 sm:px-6 py-3 sm:py-4 hidden sm:table-cell">
                                            <span class="text-gray-300 truncate max-w-xs block text-xs sm:text-sm">https://example.com/very-long-url-here</span>
                                        </td>
                                        <td class="px-4 sm:px-6 py-3 sm:py-4">
                                            <span class="text-green-400 font-semibold text-xs sm:text-sm">156</span>
                                        </td>
                                        <td class="px-4 sm:px-6 py-3 sm:py-4 hidden md:table-cell">
                                            <span class="text-gray-400 text-xs sm:text-sm">2 days ago</span>
                                        </td>
                                        <td class="px-4 sm:px-6 py-3 sm:py-4">
                                            <div class="flex space-x-1 sm:space-x-2">
                                                <button class="text-accent-blue hover:text-blue-300 transition-colors">
                                                    <i class="fas fa-chart-line text-xs sm:text-sm"></i>
                                                </button>
                                                <button class="text-purple-400 hover:text-purple-300 transition-colors">
                                                    <i class="fas fa-qrcode text-xs sm:text-sm"></i>
                                                </button>
                                                <button class="text-red-400 hover:text-red-300 transition-colors">
                                                    <i class="fas fa-trash text-xs sm:text-sm"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                @elseif($page === 'withdraw')
                    <!-- Withdraw Content -->
                    <div class="mb-6 sm:mb-8">
                        <h1 class="text-2xl sm:text-3xl font-bold mb-2">Withdraw Earnings</h1>
                        <p class="text-gray-300 text-sm sm:text-base">Request withdrawal of your earnings</p>
                    </div>

                    <!-- Balance Overview -->
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-400 text-xs sm:text-sm">Available Balance</p>
                                    <p class="text-2xl sm:text-3xl font-bold text-green-400">Rp 0</p>
                                </div>
                                <i class="fas fa-wallet text-3xl sm:text-4xl text-green-400"></i>
                            </div>
                            <p class="text-gray-400 text-xs sm:text-sm mt-2">Ready to withdraw</p>
                        </div>

                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-400 text-xs sm:text-sm">Pending</p>
                                    <p class="text-2xl sm:text-3xl font-bold text-yellow-400">Rp 0</p>
                                </div>
                                <i class="fas fa-clock text-3xl sm:text-4xl text-yellow-400"></i>
                            </div>
                            <p class="text-gray-400 text-xs sm:text-sm mt-2">Being processed</p>
                        </div>

                        <div class="glass-effect rounded-xl p-4 sm:p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-400 text-xs sm:text-sm">Total Withdrawn</p>
                                    <p class="text-2xl sm:text-3xl font-bold text-blue-400">Rp 0</p>
                                </div>
                                <i class="fas fa-history text-3xl sm:text-4xl text-blue-400"></i>
                            </div>
                            <p class="text-gray-400 text-xs sm:text-sm mt-2">All time</p>
                        </div>
                    </div>

                    <!-- Bronze Plan Notice -->
                    <div class="glass-effect rounded-xl p-4 sm:p-6 border-l-4 border-yellow-500 mb-6 sm:mb-8">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-yellow-400 mr-3 mt-1"></i>
                            <div>
                                <h4 class="font-semibold text-yellow-400 mb-2 text-sm sm:text-base">Bronze Plan Withdrawal Information</h4>
                                <ul class="text-xs sm:text-sm text-gray-300 space-y-1 mb-4">
                                    <li>• Minimum withdrawal: Rp 50,000</li>
                                    <li>• Processing time: 3-7 business days</li>
                                    <li>• Available methods: Bank Transfer, E-Wallet</li>
                                    <li>• No withdrawal fees for Bronze users</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Withdrawal Form -->
                    <div class="glass-effect rounded-xl p-4 sm:p-6">
                        <h3 class="text-lg sm:text-xl font-semibold mb-6">
                            <i class="fas fa-money-bill-wave text-green-400 mr-2"></i>
                            Request Withdrawal
                        </h3>

                        <div class="text-center py-8 sm:py-12">
                            <i class="fas fa-wallet text-4xl sm:text-6xl text-gray-600 mb-4"></i>
                            <h4 class="text-lg sm:text-xl font-semibold text-gray-400 mb-2">Insufficient Balance</h4>
                            <p class="text-gray-500 text-sm sm:text-base mb-4">You need at least Rp 50,000 to request a withdrawal.</p>
                            <p class="text-gray-500 text-sm sm:text-base">Current balance: <span class="text-red-400 font-semibold">Rp 0</span></p>
                        </div>
                    </div>

                @elseif($page === 'profile')
                    @include('bronze-plan.pages.profile')

                @elseif($page === 'settings')
                    @include('bronze-plan.pages.settings')

                @else
                    <!-- Default/Other Pages Content -->
                    <div class="mb-6 sm:mb-8">
                        <h1 class="text-2xl sm:text-3xl font-bold mb-2">{{ ucfirst(str_replace('-', ' ', $page)) }}</h1>
                        <p class="text-gray-300 text-sm sm:text-base">This feature is coming soon!</p>
                    </div>

                    <div class="glass-effect rounded-xl p-8 sm:p-12 text-center">
                        <i class="fas fa-tools text-4xl sm:text-6xl text-gray-600 mb-4"></i>
                        <h3 class="text-lg sm:text-xl font-semibold text-gray-400 mb-2">Under Development</h3>
                        <p class="text-gray-500 text-sm sm:text-base">This feature is currently being developed and will be available soon.</p>
                    </div>

                @endif
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="{{ asset('js/bronze-dashboard.js') }}"></script>

    <script>
        // Load saved avatar on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking for saved avatar...');
            const savedAvatar = localStorage.getItem('user_avatar');
            console.log('Saved avatar:', savedAvatar);

            if (savedAvatar) {
                // Update navbar avatar by ID
                const navbarAvatarId = document.querySelector('#navbar-avatar');
                if (navbarAvatarId) {
                    navbarAvatarId.src = savedAvatar;
                    console.log('Navbar avatar (ID) updated');
                }

                // Update navbar avatar by class
                const navbarAvatar = document.querySelector('.navbar-avatar');
                if (navbarAvatar) {
                    navbarAvatar.src = savedAvatar;
                    console.log('Navbar avatar (class) updated');
                }

                // Update all avatar instances
                const allAvatars = document.querySelectorAll('.user-avatar');
                console.log('Found avatars:', allAvatars.length);
                allAvatars.forEach((avatar, index) => {
                    avatar.src = savedAvatar;
                    console.log(`Avatar ${index} updated`);
                });
            }
        });

        // Global function to update all avatars
        window.updateAllAvatars = function(avatarUrl) {
            console.log('Global update all avatars:', avatarUrl);

            // Update navbar avatar by ID
            const navbarAvatarId = document.querySelector('#navbar-avatar');
            if (navbarAvatarId) {
                navbarAvatarId.src = avatarUrl;
                console.log('Global: Navbar avatar (ID) updated');
            }

            // Update navbar avatar by class
            const navbarAvatar = document.querySelector('.navbar-avatar');
            if (navbarAvatar) {
                navbarAvatar.src = avatarUrl;
                console.log('Global: Navbar avatar (class) updated');
            }

            // Update all avatar instances
            const allAvatars = document.querySelectorAll('.user-avatar');
            allAvatars.forEach((avatar, index) => {
                avatar.src = avatarUrl;
                console.log(`Global: Avatar ${index} updated`);
            });

            // Save to localStorage
            localStorage.setItem('user_avatar', avatarUrl);
        };

        // Also try to update after a short delay in case elements load later
        setTimeout(function() {
            const savedAvatar = localStorage.getItem('user_avatar');
            if (savedAvatar) {
                window.updateAllAvatars(savedAvatar);
                console.log('Delayed avatar update completed');
            }
        }, 1000);
    </script>
</body>
</html>
