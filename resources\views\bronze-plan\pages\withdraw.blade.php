<!DOCTYPE html>
<html lang="id" x-data="{ sidebarOpen: false }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdraw - Bronze Plan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-blue': '#0f172a',
                        'glass-blue': 'rgba(30, 58, 138, 0.1)',
                        'accent-blue': '#3b82f6'
                    }
                }
            }
        }
    </script>
    <style>
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(30, 58, 138, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-dark-blue via-slate-900 to-blue-900 text-white min-h-screen">
    
    <!-- Navbar -->
    <nav class="glass-effect border-b border-blue-500/20 sticky top-0 z-50">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <div class="flex items-center ml-2 lg:ml-0">
                        <img src="https://via.placeholder.com/40x40/3b82f6/ffffff?text=S4S" alt="Sub4Short Plus" class="h-8 w-8 rounded-lg">
                        <span class="ml-3 text-lg font-bold gradient-text">Sub4Short Plus</span>
                        <span class="ml-2 px-2 py-1 bg-yellow-600 text-xs rounded-full">Bronze</span>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <div class="hidden sm:flex items-center space-x-2 text-sm">
                        <i class="fas fa-coins text-yellow-400"></i>
                        <span>Rp 0</span>
                    </div>
                    <div class="relative" x-data="{ userMenuOpen: false }">
                        <button @click="userMenuOpen = !userMenuOpen" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-white/10 transition-colors">
                            <img src="https://via.placeholder.com/32x32/3b82f6/ffffff?text=U" alt="User" class="w-8 h-8 rounded-full">
                            <span class="hidden sm:block">John Doe</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        <div x-show="userMenuOpen" @click.away="userMenuOpen = false" x-transition class="absolute right-0 mt-2 w-48 glass-effect rounded-lg shadow-lg py-2">
                            <a href="#" class="block px-4 py-2 hover:bg-white/10 transition-colors">
                                <i class="fas fa-user mr-2"></i>Profile
                            </a>
                            <a href="#" class="block px-4 py-2 hover:bg-white/10 transition-colors">
                                <i class="fas fa-cog mr-2"></i>Settings
                            </a>
                            <hr class="my-2 border-gray-600">
                            <a href="#" class="block px-4 py-2 hover:bg-white/10 transition-colors text-red-400">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="fixed inset-y-0 left-0 z-40 w-64 glass-effect border-r border-blue-500/20 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
               :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'"
               style="top: 64px;">
            <div class="flex flex-col h-full pt-4">
                <nav class="flex-1 px-4 space-y-2">
                    <a href="../dashboard.blade.php" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-home mr-3"></i>
                        Dashboard
                    </a>
                    <a href="my-links.blade.php" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-link mr-3"></i>
                        My Links
                    </a>
                    <a href="analytics.blade.php" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-chart-bar mr-3"></i>
                        Analytics
                    </a>
                    <a href="qr-codes.blade.php" class="flex items-center px-4 py-3 rounded-lg hover:bg-white/10 transition-colors">
                        <i class="fas fa-qrcode mr-3"></i>
                        QR Codes
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 rounded-lg bg-accent-blue/20 text-accent-blue border border-accent-blue/30">
                        <i class="fas fa-wallet mr-3"></i>
                        Withdraw
                    </a>
                    
                    <div class="mt-8 p-4 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg border border-blue-500/30">
                        <h4 class="font-semibold mb-2">Upgrade to Platinum</h4>
                        <p class="text-sm text-gray-300 mb-3">Get unlimited links and advanced features</p>
                        <button class="w-full bg-accent-blue hover:bg-blue-600 py-2 px-4 rounded-lg text-sm font-semibold transition-colors">
                            Upgrade Now
                        </button>
                    </div>
                </nav>
            </div>
        </aside>

        <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-30 bg-black/50 lg:hidden" x-transition></div>

        <!-- Main Content -->
        <main class="flex-1 lg:ml-0 min-h-screen">
            <div class="p-6">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold mb-2">Withdraw Earnings</h1>
                    <p class="text-gray-300">Request withdrawal of your earnings</p>
                </div>

                <!-- Balance Overview -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Available Balance</p>
                                <p class="text-3xl font-bold text-green-400">Rp 0</p>
                            </div>
                            <i class="fas fa-wallet text-4xl text-green-400"></i>
                        </div>
                        <p class="text-gray-400 text-sm mt-2">Ready to withdraw</p>
                    </div>

                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Pending Withdrawals</p>
                                <p class="text-3xl font-bold text-yellow-400">Rp 0</p>
                            </div>
                            <i class="fas fa-clock text-4xl text-yellow-400"></i>
                        </div>
                        <p class="text-gray-400 text-sm mt-2">Being processed</p>
                    </div>

                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Total Withdrawn</p>
                                <p class="text-3xl font-bold text-blue-400">Rp 0</p>
                            </div>
                            <i class="fas fa-history text-4xl text-blue-400"></i>
                        </div>
                        <p class="text-gray-400 text-sm mt-2">All time</p>
                    </div>
                </div>

                <!-- Bronze Plan Notice -->
                <div class="glass-effect rounded-xl p-6 border-l-4 border-yellow-500 mb-8">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-yellow-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-semibold text-yellow-400 mb-2">Bronze Plan Withdrawal Information</h4>
                            <ul class="text-sm text-gray-300 space-y-1 mb-4">
                                <li>• Minimum withdrawal: Rp 50,000</li>
                                <li>• Processing time: 3-7 business days</li>
                                <li>• Available methods: Bank Transfer, E-Wallet</li>
                                <li>• No withdrawal fees for Bronze users</li>
                            </ul>
                            <div class="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                                <p class="text-sm text-yellow-300">
                                    <i class="fas fa-lightbulb mr-2"></i>
                                    <strong>Tip:</strong> Upgrade to Platinum for instant withdrawals and lower minimum amounts!
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Withdrawal Form -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Request Withdrawal -->
                    <div class="glass-effect rounded-xl p-6">
                        <h3 class="text-xl font-semibold mb-6">
                            <i class="fas fa-money-bill-wave text-green-400 mr-2"></i>
                            Request Withdrawal
                        </h3>

                        <form class="space-y-6" x-data="{ selectedMethod: 'bank' }">
                            <!-- Amount -->
                            <div>
                                <label class="block text-sm font-medium mb-2">
                                    <i class="fas fa-coins text-accent-blue mr-2"></i>Amount (IDR)
                                </label>
                                <input type="number" 
                                       placeholder="Minimum Rp 50,000"
                                       min="50000"
                                       class="w-full px-4 py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400"
                                       disabled>
                                <p class="text-red-400 text-sm mt-2">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    Insufficient balance. Current balance: Rp 0
                                </p>
                            </div>

                            <!-- Withdrawal Method -->
                            <div>
                                <label class="block text-sm font-medium mb-3">
                                    <i class="fas fa-credit-card text-accent-blue mr-2"></i>Withdrawal Method
                                </label>
                                <div class="space-y-3">
                                    <label class="flex items-center p-3 rounded-lg border border-blue-500/30 hover:border-accent-blue transition-colors cursor-pointer">
                                        <input type="radio" name="method" value="bank" x-model="selectedMethod" class="text-accent-blue">
                                        <i class="fas fa-university text-accent-blue mx-3"></i>
                                        <span>Bank Transfer</span>
                                    </label>
                                    <label class="flex items-center p-3 rounded-lg border border-blue-500/30 hover:border-accent-blue transition-colors cursor-pointer">
                                        <input type="radio" name="method" value="ewallet" x-model="selectedMethod" class="text-accent-blue">
                                        <i class="fas fa-mobile-alt text-accent-blue mx-3"></i>
                                        <span>E-Wallet (OVO, GoPay, DANA)</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Bank Details (shown when bank is selected) -->
                            <div x-show="selectedMethod === 'bank'" x-transition class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">Bank Name</label>
                                    <select class="w-full px-4 py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all">
                                        <option value="">Select Bank</option>
                                        <option value="bca">BCA</option>
                                        <option value="mandiri">Mandiri</option>
                                        <option value="bni">BNI</option>
                                        <option value="bri">BRI</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2">Account Number</label>
                                    <input type="text" placeholder="Enter account number" 
                                           class="w-full px-4 py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2">Account Holder Name</label>
                                    <input type="text" placeholder="Enter account holder name" 
                                           class="w-full px-4 py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400">
                                </div>
                            </div>

                            <!-- E-Wallet Details (shown when e-wallet is selected) -->
                            <div x-show="selectedMethod === 'ewallet'" x-transition class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">E-Wallet Provider</label>
                                    <select class="w-full px-4 py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all">
                                        <option value="">Select E-Wallet</option>
                                        <option value="ovo">OVO</option>
                                        <option value="gopay">GoPay</option>
                                        <option value="dana">DANA</option>
                                        <option value="linkaja">LinkAja</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2">Phone Number</label>
                                    <input type="tel" placeholder="08xxxxxxxxxx" 
                                           class="w-full px-4 py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400">
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <button type="submit" 
                                    disabled
                                    class="w-full bg-gray-600 text-gray-400 py-3 px-4 rounded-lg font-semibold cursor-not-allowed">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Request Withdrawal
                            </button>
                            <p class="text-gray-400 text-sm text-center">
                                You need at least Rp 50,000 to request a withdrawal
                            </p>
                        </form>
                    </div>

                    <!-- Withdrawal History -->
                    <div class="glass-effect rounded-xl p-6">
                        <h3 class="text-xl font-semibold mb-6">
                            <i class="fas fa-history text-accent-blue mr-2"></i>
                            Withdrawal History
                        </h3>

                        <div class="text-center py-12">
                            <i class="fas fa-inbox text-6xl text-gray-600 mb-4"></i>
                            <h4 class="text-xl font-semibold text-gray-400 mb-2">No Withdrawals Yet</h4>
                            <p class="text-gray-500">Your withdrawal history will appear here once you make your first withdrawal request.</p>
                        </div>
                    </div>
                </div>

                <!-- How to Earn -->
                <div class="mt-8 glass-effect rounded-xl p-6">
                    <h3 class="text-xl font-semibold mb-4">
                        <i class="fas fa-lightbulb text-yellow-400 mr-2"></i>
                        How to Earn Money
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="text-center p-4 bg-white/5 rounded-lg">
                            <i class="fas fa-link text-3xl text-accent-blue mb-3"></i>
                            <h4 class="font-semibold mb-2">Create Links</h4>
                            <p class="text-sm text-gray-400">Shorten URLs and share them</p>
                        </div>
                        <div class="text-center p-4 bg-white/5 rounded-lg">
                            <i class="fas fa-mouse-pointer text-3xl text-green-400 mb-3"></i>
                            <h4 class="font-semibold mb-2">Get Clicks</h4>
                            <p class="text-sm text-gray-400">Earn from every click on your links</p>
                        </div>
                        <div class="text-center p-4 bg-white/5 rounded-lg">
                            <i class="fas fa-users text-3xl text-purple-400 mb-3"></i>
                            <h4 class="font-semibold mb-2">Refer Friends</h4>
                            <p class="text-sm text-gray-400">Get bonus from referrals</p>
                        </div>
                        <div class="text-center p-4 bg-white/5 rounded-lg">
                            <i class="fas fa-crown text-3xl text-yellow-400 mb-3"></i>
                            <h4 class="font-semibold mb-2">Upgrade Plan</h4>
                            <p class="text-sm text-gray-400">Higher rates with premium plans</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

</body>
</html>
