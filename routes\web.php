<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\BronzePlanController;

Route::get('/', function () {
    return view('landing');
});

// Auth routes
Route::view('/auth/login', 'auth.login')->name('login');
Route::view('/auth/register', 'auth.register')->name('register');
Route::view('/auth/forgot-password', 'auth.forgot-password')->name('forgot-password');

// Bronze Plan Dashboard routes
Route::prefix('bronze-plan')->name('bronze.')->group(function () {
    // Main dashboard route - handles all pages via ?page= parameter
    Route::get('/dashboard', [BronzePlanController::class, 'dashboard'])->name('dashboard');

    // Individual page routes (alternative access)
    Route::get('/my-links', function () {
        return redirect()->route('bronze.dashboard', ['page' => 'my-links']);
    })->name('my-links');

    Route::get('/analytics', function () {
        return redirect()->route('bronze.dashboard', ['page' => 'analytics']);
    })->name('analytics');

    Route::get('/qr-codes', function () {
        return redirect()->route('bronze.dashboard', ['page' => 'qr-codes']);
    })->name('qr-codes');

    Route::get('/withdraw', function () {
        return redirect()->route('bronze.dashboard', ['page' => 'withdraw']);
    })->name('withdraw');



    // Avatar Upload Route
    Route::post('/upload-avatar', function (Request $request) {
        try {
            $request->validate([
                'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            ]);

            if ($request->hasFile('avatar')) {
                $file = $request->file('avatar');
                $filename = 'avatar_' . (auth()->id() ?? 'guest') . '_' . time() . '.' . $file->getClientOriginalExtension();

                // Store in storage/app/public/img/avatars
                $path = $file->storeAs('public/img/avatars', $filename);

                // Generate the public URL
                $url = Storage::url($path);

                // Make sure the file exists
                if (Storage::exists($path)) {
                    return response()->json([
                        'success' => true,
                        'avatar_url' => $url,
                        'filename' => $filename,
                        'path' => $path,
                        'message' => 'Avatar uploaded successfully!'
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'File upload failed - file not found after upload'
                    ], 500);
                }
            }

            return response()->json([
                'success' => false,
                'message' => 'No file uploaded'
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Upload error: ' . $e->getMessage()
            ], 500);
        }
    })->name('upload-avatar');

    // API routes for AJAX functionality
    Route::post('/create-link', [BronzePlanController::class, 'createLink'])->name('create-link');
    Route::post('/generate-qr', [BronzePlanController::class, 'generateQR'])->name('generate-qr');
    Route::post('/request-withdrawal', [BronzePlanController::class, 'requestWithdrawal'])->name('request-withdrawal');
    Route::delete('/delete-link/{id}', [BronzePlanController::class, 'deleteLink'])->name('delete-link');
    Route::get('/analytics-data', [BronzePlanController::class, 'getAnalytics'])->name('analytics-data');
});
