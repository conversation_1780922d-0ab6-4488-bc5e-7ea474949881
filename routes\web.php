<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BronzePlanController;

Route::get('/', function () {
    return view('landing');
});

// Auth routes
Route::view('/auth/login', 'auth.login')->name('login');
Route::view('/auth/register', 'auth.register')->name('register');
Route::view('/auth/forgot-password', 'auth.forgot-password')->name('forgot-password');

// Bronze Plan Dashboard routes
Route::prefix('bronze-plan')->name('bronze.')->group(function () {
    // Main dashboard route - handles all pages via ?page= parameter
    Route::get('/dashboard', [BronzePlanController::class, 'dashboard'])->name('dashboard');

    // Individual page routes (alternative access)
    Route::get('/my-links', function () {
        return redirect()->route('bronze.dashboard', ['page' => 'my-links']);
    })->name('my-links');

    Route::get('/analytics', function () {
        return redirect()->route('bronze.dashboard', ['page' => 'analytics']);
    })->name('analytics');

    Route::get('/qr-codes', function () {
        return redirect()->route('bronze.dashboard', ['page' => 'qr-codes']);
    })->name('qr-codes');

    Route::get('/withdraw', function () {
        return redirect()->route('bronze.dashboard', ['page' => 'withdraw']);
    })->name('withdraw');

    // API routes for AJAX functionality
    Route::post('/create-link', [BronzePlanController::class, 'createLink'])->name('create-link');
    Route::post('/generate-qr', [BronzePlanController::class, 'generateQR'])->name('generate-qr');
    Route::post('/request-withdrawal', [BronzePlanController::class, 'requestWithdrawal'])->name('request-withdrawal');
    Route::delete('/delete-link/{id}', [BronzePlanController::class, 'deleteLink'])->name('delete-link');
    Route::get('/analytics-data', [BronzePlanController::class, 'getAnalytics'])->name('analytics-data');
});
