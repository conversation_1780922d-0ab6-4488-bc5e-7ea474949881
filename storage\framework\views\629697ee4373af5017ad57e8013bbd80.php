<!-- Settings Page Content -->
<div class="space-y-4 sm:space-y-6" x-data="settingsManager()">
    <!-- Toastr Container -->
    <div id="settings-toastr-container" class="fixed top-20 right-4 z-50 space-y-2"></div>

    <style>
        .settings-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(30, 41, 59, 0.9) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.4);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .settings-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .form-input {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15);
            outline: none;
            background: rgba(30, 41, 59, 0.9);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 56px;
            height: 28px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            transition: 0.4s;
            border-radius: 28px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 3px;
            bottom: 3px;
            background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
            transition: 0.4s;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        input:checked + .toggle-slider {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(28px);
        }

        .loading-spinner {
            border: 3px solid rgba(59, 130, 246, 0.3);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .toastr {
            padding: 16px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: slideIn 0.3s ease-out;
            max-width: 400px;
            word-wrap: break-word;
        }

        .toastr.success {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(22, 163, 74, 0.9) 100%);
            border-color: rgba(34, 197, 94, 0.3);
        }

        .toastr.error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
            border-color: rgba(239, 68, 68, 0.3);
        }

        .toastr.warning {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.9) 0%, rgba(217, 119, 6, 0.9) 100%);
            border-color: rgba(245, 158, 11, 0.3);
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    </style>

    <!-- Page Header -->
    <div class="settings-card rounded-2xl p-4 sm:p-6 lg:p-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    <i class="fas fa-cog mr-2 sm:mr-3"></i>Settings
                </h1>
                <p class="text-gray-400 mt-1 sm:mt-2 text-sm sm:text-base">Manage your account preferences and configurations</p>
            </div>
            <div class="mt-3 sm:mt-0">
                <span class="inline-block px-3 sm:px-4 py-1.5 sm:py-2 bg-yellow-600/20 border border-yellow-500/30 rounded-full text-yellow-400 text-xs sm:text-sm font-medium">
                    <i class="fas fa-crown mr-1 sm:mr-2"></i>Bronze Plan
                </span>
            </div>
        </div>
    </div>

    <!-- Settings Container -->
    <div class="settings-card rounded-2xl overflow-hidden">
        <!-- Tab Navigation -->
        <div class="border-b border-blue-500/20 overflow-x-auto">
            <nav class="flex space-x-4 sm:space-x-8 px-4 sm:px-6 lg:px-8 min-w-max" aria-label="Tabs">
                <button @click="activeTab = 'general'" 
                        :class="activeTab === 'general' ? 'border-blue-500 text-blue-400' : 'border-transparent text-gray-400 hover:text-gray-300'"
                        class="whitespace-nowrap py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm transition-colors">
                    <i class="fas fa-sliders-h mr-1 sm:mr-2"></i>General
                </button>
                <button @click="activeTab = 'security'" 
                        :class="activeTab === 'security' ? 'border-blue-500 text-blue-400' : 'border-transparent text-gray-400 hover:text-gray-300'"
                        class="whitespace-nowrap py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm transition-colors">
                    <i class="fas fa-shield-alt mr-1 sm:mr-2"></i>Security
                </button>
                <button @click="activeTab = 'notifications'" 
                        :class="activeTab === 'notifications' ? 'border-blue-500 text-blue-400' : 'border-transparent text-gray-400 hover:text-gray-300'"
                        class="whitespace-nowrap py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm transition-colors">
                    <i class="fas fa-bell mr-1 sm:mr-2"></i>Notifications
                </button>
                <button @click="activeTab = 'billing'" 
                        :class="activeTab === 'billing' ? 'border-blue-500 text-blue-400' : 'border-transparent text-gray-400 hover:text-gray-300'"
                        class="whitespace-nowrap py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm transition-colors">
                    <i class="fas fa-credit-card mr-1 sm:mr-2"></i>Billing
                </button>
                <button @click="activeTab = 'advanced'" 
                        :class="activeTab === 'advanced' ? 'border-blue-500 text-blue-400' : 'border-transparent text-gray-400 hover:text-gray-300'"
                        class="whitespace-nowrap py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm transition-colors">
                    <i class="fas fa-cogs mr-1 sm:mr-2"></i>Advanced
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-4 sm:p-6 lg:p-8">
            <!-- General Tab -->
            <div x-show="activeTab === 'general'" x-transition class="space-y-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg sm:text-xl font-semibold text-white">General Settings</h3>
                    <button @click="saveGeneralSettings"
                            :disabled="saving"
                            class="btn-primary px-4 py-2 rounded-lg text-sm font-medium text-white flex items-center">
                        <div x-show="saving" class="loading-spinner w-4 h-4 mr-2"></div>
                        <i x-show="!saving" class="fas fa-save mr-2"></i>
                        <span x-text="saving ? 'Saving...' : 'Save Changes'"></span>
                    </button>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Language Settings -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-language mr-2 text-blue-400"></i>Language
                            </label>
                            <select x-model="settings.general.language"
                                    class="form-input w-full px-4 py-3 rounded-xl text-white text-sm">
                                <option value="en">English</option>
                                <option value="id">Bahasa Indonesia</option>
                                <option value="es">Español</option>
                                <option value="fr">Français</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-clock mr-2 text-blue-400"></i>Timezone
                            </label>
                            <select x-model="settings.general.timezone"
                                    class="form-input w-full px-4 py-3 rounded-xl text-white text-sm">
                                <option value="Asia/Jakarta">Asia/Jakarta (WIB)</option>
                                <option value="Asia/Makassar">Asia/Makassar (WITA)</option>
                                <option value="Asia/Jayapura">Asia/Jayapura (WIT)</option>
                                <option value="UTC">UTC</option>
                                <option value="America/New_York">America/New_York</option>
                                <option value="Europe/London">Europe/London</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-calendar mr-2 text-blue-400"></i>Date Format
                            </label>
                            <select x-model="settings.general.dateFormat"
                                    class="form-input w-full px-4 py-3 rounded-xl text-white text-sm">
                                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                <option value="DD MMM YYYY">DD MMM YYYY</option>
                            </select>
                        </div>
                    </div>

                    <!-- Display Settings -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-palette mr-2 text-blue-400"></i>Theme
                            </label>
                            <div class="grid grid-cols-2 gap-3">
                                <button @click="settings.general.theme = 'dark'"
                                        :class="settings.general.theme === 'dark' ? 'ring-2 ring-blue-500' : ''"
                                        class="p-4 bg-slate-800 border border-blue-500/30 rounded-xl hover:border-blue-500/50 transition-colors">
                                    <i class="fas fa-moon text-blue-400 mb-2"></i>
                                    <div class="text-sm text-white">Dark</div>
                                </button>
                                <button @click="settings.general.theme = 'light'"
                                        :class="settings.general.theme === 'light' ? 'ring-2 ring-blue-500' : ''"
                                        class="p-4 bg-slate-800 border border-blue-500/30 rounded-xl hover:border-blue-500/50 transition-colors">
                                    <i class="fas fa-sun text-yellow-400 mb-2"></i>
                                    <div class="text-sm text-white">Light</div>
                                </button>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-link mr-2 text-blue-400"></i>Default URL Length
                            </label>
                            <select x-model="settings.general.defaultUrlLength"
                                    class="form-input w-full px-4 py-3 rounded-xl text-white text-sm">
                                <option value="4">4 characters</option>
                                <option value="5">5 characters</option>
                                <option value="6">6 characters</option>
                                <option value="7">7 characters</option>
                                <option value="8">8 characters</option>
                            </select>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Auto-copy shortened URLs</div>
                                <div class="text-xs text-gray-400">Automatically copy URLs to clipboard</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.general.autoCopy">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Tab -->
            <div x-show="activeTab === 'security'" x-transition class="space-y-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg sm:text-xl font-semibold text-white">Security Settings</h3>
                    <button @click="saveSecuritySettings"
                            :disabled="saving"
                            class="btn-primary px-4 py-2 rounded-lg text-sm font-medium text-white flex items-center">
                        <div x-show="saving" class="loading-spinner w-4 h-4 mr-2"></div>
                        <i x-show="!saving" class="fas fa-shield-alt mr-2"></i>
                        <span x-text="saving ? 'Saving...' : 'Save Changes'"></span>
                    </button>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Password Settings -->
                    <div class="space-y-4">
                        <h4 class="text-md font-semibold text-blue-400 border-b border-blue-500/20 pb-2">
                            <i class="fas fa-key mr-2"></i>Password & Authentication
                        </h4>

                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">Current Password</label>
                            <input type="password" x-model="passwordForm.currentPassword"
                                   class="form-input w-full px-4 py-3 rounded-xl text-white text-sm"
                                   placeholder="Enter current password">
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">New Password</label>
                            <input type="password" x-model="passwordForm.newPassword"
                                   class="form-input w-full px-4 py-3 rounded-xl text-white text-sm"
                                   placeholder="Enter new password">
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">Confirm New Password</label>
                            <input type="password" x-model="passwordForm.confirmPassword"
                                   class="form-input w-full px-4 py-3 rounded-xl text-white text-sm"
                                   placeholder="Confirm new password">
                        </div>

                        <button @click="changePassword"
                                :disabled="changingPassword"
                                class="btn-primary w-full px-4 py-3 rounded-xl text-sm font-medium text-white flex items-center justify-center">
                            <div x-show="changingPassword" class="loading-spinner w-4 h-4 mr-2"></div>
                            <i x-show="!changingPassword" class="fas fa-lock mr-2"></i>
                            <span x-text="changingPassword ? 'Changing Password...' : 'Change Password'"></span>
                        </button>
                    </div>

                    <!-- Security Options -->
                    <div class="space-y-4">
                        <h4 class="text-md font-semibold text-blue-400 border-b border-blue-500/20 pb-2">
                            <i class="fas fa-shield-alt mr-2"></i>Security Options
                        </h4>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Two-Factor Authentication</div>
                                <div class="text-xs text-gray-400">Add an extra layer of security</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.security.twoFactorAuth">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Login Notifications</div>
                                <div class="text-xs text-gray-400">Get notified of new logins</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.security.loginNotifications">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Session Timeout</div>
                                <div class="text-xs text-gray-400">Auto-logout after inactivity</div>
                            </div>
                            <select x-model="settings.security.sessionTimeout"
                                    class="form-input px-3 py-2 rounded-lg text-white text-sm w-32">
                                <option value="30">30 min</option>
                                <option value="60">1 hour</option>
                                <option value="120">2 hours</option>
                                <option value="240">4 hours</option>
                                <option value="0">Never</option>
                            </select>
                        </div>

                        <div class="p-4 bg-red-900/20 border border-red-500/30 rounded-xl">
                            <h5 class="text-sm font-semibold text-red-400 mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Danger Zone
                            </h5>
                            <p class="text-xs text-gray-400 mb-3">These actions cannot be undone</p>
                            <button @click="showDeleteAccountModal = true"
                                    class="btn-danger px-4 py-2 rounded-lg text-sm font-medium text-white">
                                <i class="fas fa-trash mr-2"></i>Delete Account
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications Tab -->
            <div x-show="activeTab === 'notifications'" x-transition class="space-y-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg sm:text-xl font-semibold text-white">Notification Preferences</h3>
                    <button @click="saveNotificationSettings"
                            :disabled="saving"
                            class="btn-primary px-4 py-2 rounded-lg text-sm font-medium text-white flex items-center">
                        <div x-show="saving" class="loading-spinner w-4 h-4 mr-2"></div>
                        <i x-show="!saving" class="fas fa-bell mr-2"></i>
                        <span x-text="saving ? 'Saving...' : 'Save Changes'"></span>
                    </button>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Email Notifications -->
                    <div class="space-y-4">
                        <h4 class="text-md font-semibold text-blue-400 border-b border-blue-500/20 pb-2">
                            <i class="fas fa-envelope mr-2"></i>Email Notifications
                        </h4>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">URL Analytics Reports</div>
                                <div class="text-xs text-gray-400">Weekly analytics summary</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.notifications.email.analytics">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Security Alerts</div>
                                <div class="text-xs text-gray-400">Login attempts and security events</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.notifications.email.security">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Account Updates</div>
                                <div class="text-xs text-gray-400">Plan changes and billing updates</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.notifications.email.account">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Marketing & Promotions</div>
                                <div class="text-xs text-gray-400">New features and special offers</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.notifications.email.marketing">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>

                    <!-- Push Notifications -->
                    <div class="space-y-4">
                        <h4 class="text-md font-semibold text-blue-400 border-b border-blue-500/20 pb-2">
                            <i class="fas fa-mobile-alt mr-2"></i>Push Notifications
                        </h4>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Real-time Click Alerts</div>
                                <div class="text-xs text-gray-400">Get notified when URLs are clicked</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.notifications.push.clicks">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Daily Summary</div>
                                <div class="text-xs text-gray-400">Daily performance summary</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.notifications.push.summary">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">System Maintenance</div>
                                <div class="text-xs text-gray-400">Scheduled maintenance notifications</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.notifications.push.maintenance">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <!-- Notification Frequency -->
                        <div class="p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-clock mr-2 text-blue-400"></i>Notification Frequency
                            </label>
                            <select x-model="settings.notifications.frequency"
                                    class="form-input w-full px-4 py-3 rounded-xl text-white text-sm">
                                <option value="instant">Instant</option>
                                <option value="hourly">Hourly Digest</option>
                                <option value="daily">Daily Digest</option>
                                <option value="weekly">Weekly Digest</option>
                            </select>
                        </div>

                        <!-- Quiet Hours -->
                        <div class="p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div class="flex items-center justify-between mb-3">
                                <label class="text-sm font-semibold text-gray-300">
                                    <i class="fas fa-moon mr-2 text-blue-400"></i>Quiet Hours
                                </label>
                                <label class="toggle-switch">
                                    <input type="checkbox" x-model="settings.notifications.quietHours.enabled">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div x-show="settings.notifications.quietHours.enabled" class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-xs text-gray-400 mb-1">From</label>
                                    <input type="time" x-model="settings.notifications.quietHours.from"
                                           class="form-input w-full px-3 py-2 rounded-lg text-white text-sm">
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-400 mb-1">To</label>
                                    <input type="time" x-model="settings.notifications.quietHours.to"
                                           class="form-input w-full px-3 py-2 rounded-lg text-white text-sm">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Tab -->
            <div x-show="activeTab === 'billing'" x-transition class="space-y-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg sm:text-xl font-semibold text-white">Billing & Subscription</h3>
                    <div class="flex space-x-3">
                        <button @click="downloadInvoice"
                                class="btn-primary px-4 py-2 rounded-lg text-sm font-medium text-white flex items-center">
                            <i class="fas fa-download mr-2"></i>Download Invoice
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Current Plan -->
                    <div class="space-y-4">
                        <h4 class="text-md font-semibold text-blue-400 border-b border-blue-500/20 pb-2">
                            <i class="fas fa-crown mr-2"></i>Current Plan
                        </h4>

                        <div class="p-6 bg-gradient-to-br from-yellow-600/20 to-yellow-700/20 border border-yellow-500/30 rounded-xl">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h5 class="text-lg font-bold text-yellow-400">Bronze Plan</h5>
                                    <p class="text-sm text-gray-300">Perfect for getting started</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">FREE</div>
                                    <div class="text-xs text-gray-400">Forever</div>
                                </div>
                            </div>

                            <div class="space-y-2 text-sm">
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    Up to 100 URLs per month
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    Basic analytics
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    Standard support
                                </div>
                            </div>
                        </div>

                        <!-- Usage Stats -->
                        <div class="p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <h5 class="text-sm font-semibold text-white mb-3">
                                <i class="fas fa-chart-bar mr-2 text-blue-400"></i>Usage This Month
                            </h5>
                            <div class="space-y-3">
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-300">URLs Created</span>
                                        <span class="text-white">23 / 100</span>
                                    </div>
                                    <div class="w-full bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 23%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-300">Total Clicks</span>
                                        <span class="text-white">1,247</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Upgrade Options -->
                    <div class="space-y-4">
                        <h4 class="text-md font-semibold text-blue-400 border-b border-blue-500/20 pb-2">
                            <i class="fas fa-rocket mr-2"></i>Upgrade Options
                        </h4>

                        <!-- Platinum Plan -->
                        <div class="p-6 bg-gradient-to-br from-blue-600/20 to-blue-700/20 border border-blue-500/30 rounded-xl hover:border-blue-400/50 transition-colors">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h5 class="text-lg font-bold text-blue-400">Platinum Plan</h5>
                                    <p class="text-sm text-gray-300">For growing businesses</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">50K IDR</div>
                                    <div class="text-xs text-gray-400">per month</div>
                                </div>
                            </div>

                            <div class="space-y-2 text-sm mb-4">
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    Up to 1,000 URLs per month
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    Advanced analytics
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    Custom domains
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    Priority support
                                </div>
                            </div>

                            <button @click="upgradePlan('platinum')"
                                    class="btn-primary w-full px-4 py-3 rounded-xl text-sm font-medium text-white">
                                <i class="fas fa-arrow-up mr-2"></i>Upgrade to Platinum
                            </button>
                        </div>

                        <!-- Master Plan -->
                        <div class="p-6 bg-gradient-to-br from-purple-600/20 to-purple-700/20 border border-purple-500/30 rounded-xl hover:border-purple-400/50 transition-colors">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h5 class="text-lg font-bold text-purple-400">Master Plan</h5>
                                    <p class="text-sm text-gray-300">For power users</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-white">150K IDR</div>
                                    <div class="text-xs text-gray-400">per month</div>
                                </div>
                            </div>

                            <div class="space-y-2 text-sm mb-4">
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    Unlimited URLs
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    Premium analytics & reports
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    Multiple custom domains
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    API access
                                </div>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-2 w-4"></i>
                                    24/7 premium support
                                </div>
                            </div>

                            <button @click="upgradePlan('master')"
                                    class="btn-primary w-full px-4 py-3 rounded-xl text-sm font-medium text-white">
                                <i class="fas fa-crown mr-2"></i>Upgrade to Master
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Tab -->
            <div x-show="activeTab === 'advanced'" x-transition class="space-y-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg sm:text-xl font-semibold text-white">Advanced Settings</h3>
                    <button @click="saveAdvancedSettings"
                            :disabled="saving"
                            class="btn-primary px-4 py-2 rounded-lg text-sm font-medium text-white flex items-center">
                        <div x-show="saving" class="loading-spinner w-4 h-4 mr-2"></div>
                        <i x-show="!saving" class="fas fa-cogs mr-2"></i>
                        <span x-text="saving ? 'Saving...' : 'Save Changes'"></span>
                    </button>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- API Settings -->
                    <div class="space-y-4">
                        <h4 class="text-md font-semibold text-blue-400 border-b border-blue-500/20 pb-2">
                            <i class="fas fa-code mr-2"></i>API Configuration
                        </h4>

                        <div class="p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div class="flex items-center justify-between mb-3">
                                <label class="text-sm font-semibold text-gray-300">API Access</label>
                                <span class="px-2 py-1 bg-yellow-600/20 border border-yellow-500/30 rounded text-yellow-400 text-xs">
                                    Master Plan Required
                                </span>
                            </div>
                            <p class="text-xs text-gray-400 mb-3">Generate API keys to integrate with your applications</p>
                            <button disabled class="btn-primary opacity-50 cursor-not-allowed w-full px-4 py-2 rounded-lg text-sm">
                                <i class="fas fa-key mr-2"></i>Generate API Key
                            </button>
                        </div>

                        <div class="p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-tachometer-alt mr-2 text-blue-400"></i>Rate Limiting
                            </label>
                            <select x-model="settings.advanced.rateLimit"
                                    class="form-input w-full px-4 py-3 rounded-xl text-white text-sm">
                                <option value="100">100 requests/hour</option>
                                <option value="500">500 requests/hour</option>
                                <option value="1000">1000 requests/hour</option>
                                <option value="unlimited">Unlimited (Master Plan)</option>
                            </select>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Webhook Notifications</div>
                                <div class="text-xs text-gray-400">Send click events to your endpoint</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.advanced.webhooks" disabled>
                                <span class="toggle-slider opacity-50"></span>
                            </label>
                        </div>
                    </div>

                    <!-- Data & Privacy -->
                    <div class="space-y-4">
                        <h4 class="text-md font-semibold text-blue-400 border-b border-blue-500/20 pb-2">
                            <i class="fas fa-database mr-2"></i>Data & Privacy
                        </h4>

                        <div class="p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-history mr-2 text-blue-400"></i>Data Retention
                            </label>
                            <select x-model="settings.advanced.dataRetention"
                                    class="form-input w-full px-4 py-3 rounded-xl text-white text-sm">
                                <option value="30">30 days</option>
                                <option value="90">90 days</option>
                                <option value="365">1 year</option>
                                <option value="forever">Forever</option>
                            </select>
                            <p class="text-xs text-gray-400 mt-2">How long to keep click analytics data</p>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">Anonymous Analytics</div>
                                <div class="text-xs text-gray-400">Hide visitor IP addresses</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.advanced.anonymousAnalytics">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <div>
                                <div class="text-sm font-medium text-white">GDPR Compliance</div>
                                <div class="text-xs text-gray-400">Enable GDPR-compliant data handling</div>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" x-model="settings.advanced.gdprCompliance">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <!-- Export Data -->
                        <div class="p-4 bg-slate-800/50 rounded-xl border border-blue-500/20">
                            <h5 class="text-sm font-semibold text-white mb-3">
                                <i class="fas fa-download mr-2 text-blue-400"></i>Export Your Data
                            </h5>
                            <p class="text-xs text-gray-400 mb-3">Download all your data in JSON format</p>
                            <button @click="exportData"
                                    :disabled="exporting"
                                    class="btn-primary w-full px-4 py-2 rounded-lg text-sm font-medium text-white flex items-center justify-center">
                                <div x-show="exporting" class="loading-spinner w-4 h-4 mr-2"></div>
                                <i x-show="!exporting" class="fas fa-file-export mr-2"></i>
                                <span x-text="exporting ? 'Exporting...' : 'Export Data'"></span>
                            </button>
                        </div>

                        <!-- Reset Settings -->
                        <div class="p-4 bg-red-900/20 border border-red-500/30 rounded-xl">
                            <h5 class="text-sm font-semibold text-red-400 mb-2">
                                <i class="fas fa-undo mr-2"></i>Reset Settings
                            </h5>
                            <p class="text-xs text-gray-400 mb-3">Reset all settings to default values</p>
                            <button @click="resetSettings"
                                    class="btn-danger px-4 py-2 rounded-lg text-sm font-medium text-white">
                                <i class="fas fa-refresh mr-2"></i>Reset to Defaults
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function settingsManager() {
    return {
        activeTab: 'general',
        saving: false,
        changingPassword: false,
        exporting: false,
        showDeleteAccountModal: false,

        // Settings data
        settings: {
            general: {
                language: 'en',
                timezone: 'Asia/Jakarta',
                dateFormat: 'DD/MM/YYYY',
                theme: 'dark',
                defaultUrlLength: '6',
                autoCopy: true
            },
            security: {
                twoFactorAuth: false,
                loginNotifications: true,
                sessionTimeout: '60'
            },
            notifications: {
                email: {
                    analytics: true,
                    security: true,
                    account: true,
                    marketing: false
                },
                push: {
                    clicks: false,
                    summary: true,
                    maintenance: true
                },
                frequency: 'daily',
                quietHours: {
                    enabled: false,
                    from: '22:00',
                    to: '08:00'
                }
            },
            advanced: {
                rateLimit: '100',
                webhooks: false,
                dataRetention: '90',
                anonymousAnalytics: false,
                gdprCompliance: true
            }
        },

        // Password form
        passwordForm: {
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
        },

        init() {
            // Load settings from localStorage if available
            const savedSettings = localStorage.getItem('user_settings');
            if (savedSettings) {
                this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
            }
        },

        // Save functions for each tab
        async saveGeneralSettings() {
            this.saving = true;
            try {
                // Simulate API call
                await this.delay(1500);

                // Save to localStorage
                localStorage.setItem('user_settings', JSON.stringify(this.settings));

                this.showToastr('General settings saved successfully!', 'success');
                this.playSuccessSound();
            } catch (error) {
                this.showToastr('Failed to save settings. Please try again.', 'error');
            } finally {
                this.saving = false;
            }
        },

        async saveSecuritySettings() {
            this.saving = true;
            try {
                await this.delay(1500);
                localStorage.setItem('user_settings', JSON.stringify(this.settings));
                this.showToastr('Security settings updated successfully!', 'success');
                this.playSuccessSound();
            } catch (error) {
                this.showToastr('Failed to update security settings.', 'error');
            } finally {
                this.saving = false;
            }
        },

        async saveNotificationSettings() {
            this.saving = true;
            try {
                await this.delay(1500);
                localStorage.setItem('user_settings', JSON.stringify(this.settings));
                this.showToastr('Notification preferences saved!', 'success');
                this.playSuccessSound();
            } catch (error) {
                this.showToastr('Failed to save notification settings.', 'error');
            } finally {
                this.saving = false;
            }
        },

        async saveAdvancedSettings() {
            this.saving = true;
            try {
                await this.delay(1500);
                localStorage.setItem('user_settings', JSON.stringify(this.settings));
                this.showToastr('Advanced settings saved successfully!', 'success');
                this.playSuccessSound();
            } catch (error) {
                this.showToastr('Failed to save advanced settings.', 'error');
            } finally {
                this.saving = false;
            }
        },

        // Password change
        async changePassword() {
            if (!this.passwordForm.currentPassword || !this.passwordForm.newPassword || !this.passwordForm.confirmPassword) {
                this.showToastr('Please fill in all password fields.', 'warning');
                return;
            }

            if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
                this.showToastr('New passwords do not match.', 'error');
                return;
            }

            if (this.passwordForm.newPassword.length < 8) {
                this.showToastr('Password must be at least 8 characters long.', 'warning');
                return;
            }

            this.changingPassword = true;
            try {
                await this.delay(2000);

                // Clear form
                this.passwordForm = {
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: ''
                };

                this.showToastr('Password changed successfully!', 'success');
                this.playSuccessSound();
            } catch (error) {
                this.showToastr('Failed to change password. Please try again.', 'error');
            } finally {
                this.changingPassword = false;
            }
        },

        // Billing functions
        upgradePlan(plan) {
            this.showToastr(`Redirecting to ${plan} plan upgrade...`, 'success');
            // In real app, redirect to payment page
            setTimeout(() => {
                window.location.href = `/upgrade/${plan}`;
            }, 1500);
        },

        downloadInvoice() {
            this.showToastr('Downloading latest invoice...', 'success');
            // Simulate download
            setTimeout(() => {
                const link = document.createElement('a');
                link.href = 'data:text/plain;charset=utf-8,Invoice%20Content%20Here';
                link.download = 'invoice.pdf';
                link.click();
            }, 1000);
        },

        // Advanced functions
        async exportData() {
            this.exporting = true;
            try {
                await this.delay(3000);

                const data = {
                    settings: this.settings,
                    exportDate: new Date().toISOString(),
                    plan: 'bronze'
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = 'sub4short-data-export.json';
                link.click();
                URL.revokeObjectURL(url);

                this.showToastr('Data exported successfully!', 'success');
                this.playSuccessSound();
            } catch (error) {
                this.showToastr('Failed to export data.', 'error');
            } finally {
                this.exporting = false;
            }
        },

        resetSettings() {
            if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
                // Reset to default values
                this.settings = {
                    general: {
                        language: 'en',
                        timezone: 'Asia/Jakarta',
                        dateFormat: 'DD/MM/YYYY',
                        theme: 'dark',
                        defaultUrlLength: '6',
                        autoCopy: true
                    },
                    security: {
                        twoFactorAuth: false,
                        loginNotifications: true,
                        sessionTimeout: '60'
                    },
                    notifications: {
                        email: {
                            analytics: true,
                            security: true,
                            account: true,
                            marketing: false
                        },
                        push: {
                            clicks: false,
                            summary: true,
                            maintenance: true
                        },
                        frequency: 'daily',
                        quietHours: {
                            enabled: false,
                            from: '22:00',
                            to: '08:00'
                        }
                    },
                    advanced: {
                        rateLimit: '100',
                        webhooks: false,
                        dataRetention: '90',
                        anonymousAnalytics: false,
                        gdprCompliance: true
                    }
                };

                localStorage.removeItem('user_settings');
                this.showToastr('Settings reset to default values!', 'success');
                this.playSuccessSound();
            }
        },

        // Utility functions
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },

        playSuccessSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
                oscillator.frequency.setValueAtTime(1200, audioContext.currentTime + 0.2);

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.3);
            } catch (error) {
                console.log('Audio not supported');
            }
        },

        showToastr(message, type = 'success') {
            const container = document.getElementById('settings-toastr-container');
            const toastr = document.createElement('div');
            toastr.className = `toastr ${type}`;

            const icon = type === 'success' ? 'fas fa-check-circle' :
                        type === 'error' ? 'fas fa-exclamation-circle' :
                        'fas fa-exclamation-triangle';

            toastr.innerHTML = `
                <div class="flex items-center">
                    <i class="${icon} mr-3"></i>
                    <span>${message}</span>
                </div>
            `;

            container.appendChild(toastr);

            // Auto remove after 4 seconds
            setTimeout(() => {
                if (toastr.parentNode) {
                    toastr.style.animation = 'slideOut 0.3s ease-in forwards';
                    setTimeout(() => {
                        if (toastr.parentNode) {
                            toastr.parentNode.removeChild(toastr);
                        }
                    }, 300);
                }
            }, 4000);
        }
    }
}
</script>
<?php /**PATH C:\laragon\www\sub4short-plus\resources\views/bronze-plan/pages/settings.blade.php ENDPATH**/ ?>