<!DOCTYPE html>
<html lang="id" x-data="{ sidebarOpen: false, activeTab: 'account' }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Sub4Short Plus</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-blue': '#0f172a',
                        'accent-blue': '#3b82f6'
                    }
                }
            }
        }
    </script>
    <style>
        .glass-effect {
            background: rgba(30, 41, 59, 0.7);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        .layout-container {
            height: 100vh;
            overflow: hidden;
            position: relative;
        }
        
        .navbar-fixed {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            height: 64px;
        }
        
        .sidebar-fixed {
            position: fixed;
            top: 64px;
            left: 0;
            bottom: 0;
            z-index: 40;
            height: calc(100vh - 64px);
        }
        
        .content-area {
            margin-top: 64px;
            margin-left: 0;
            width: 100%;
        }
        
        @media (min-width: 1024px) {
            .content-area {
                margin-left: 16rem;
                width: calc(100% - 16rem);
            }
        }
        
        .main-content-scroll {
            height: calc(100vh - 64px);
            overflow-y: auto;
            overflow-x: hidden;
            width: 100%;
        }
        
        .main-content-scroll::-webkit-scrollbar {
            width: 6px;
        }
        .main-content-scroll::-webkit-scrollbar-track {
            background: rgba(59, 130, 246, 0.1);
            border-radius: 3px;
        }
        .main-content-scroll::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.3);
            border-radius: 3px;
        }
        .main-content-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.5);
        }
        
        .form-input {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }
        
        .tab-button {
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            background: rgba(59, 130, 246, 0.2);
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 48px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #374151;
            transition: 0.3s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: #3b82f6;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(24px);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-dark-blue via-slate-900 to-blue-900 text-white overflow-x-hidden">
    <div class="layout-container">
        <!-- Navbar -->
        <nav class="navbar-fixed glass-effect border-b border-blue-500/20">
            <div class="px-3 sm:px-4 lg:px-6">
                <div class="flex justify-between items-center h-16">
                    <!-- Left: Logo & Mobile Menu Button -->
                    <div class="flex items-center">
                        <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors mr-2">
                            <i class="fas fa-bars text-lg"></i>
                        </button>
                        <div class="flex items-center space-x-2 sm:space-x-3">
                            <div class="w-8 h-8 sm:w-10 sm:h-10 bg-accent-blue rounded-lg flex items-center justify-center">
                                <i class="fas fa-link text-white text-sm sm:text-base"></i>
                            </div>
                            <div>
                                <h1 class="text-lg sm:text-xl font-bold">Sub4Short</h1>
                                <p class="text-xs text-gray-400 hidden sm:block">Plus</p>
                            </div>
                        </div>
                    </div>

                    <!-- Right: Back to Dashboard -->
                    <div class="flex items-center space-x-4">
                        <a href="<?php echo e(route('bronze.dashboard')); ?>" class="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-white/10 transition-colors text-sm">
                            <i class="fas fa-arrow-left"></i>
                            <span class="hidden sm:inline">Back to Dashboard</span>
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Sidebar -->
        <aside class="sidebar-fixed w-56 sm:w-64 glass-effect border-r border-blue-500/20 transform transition-transform duration-300 ease-in-out lg:translate-x-0"
               :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'">
            <div class="flex flex-col h-full pt-3 sm:pt-4 pb-4">
                <!-- Profile Section -->
                <div class="px-3 sm:px-4 mb-6">
                    <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/5">
                        <img src="https://via.placeholder.com/40x40/3b82f6/ffffff?text=JD" alt="Profile" class="w-10 h-10 rounded-full">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium truncate">John Doe</p>
                            <p class="text-xs text-gray-400 truncate">Bronze Plan</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="flex-1 px-3 sm:px-4 space-y-1">
                    <a href="<?php echo e(route('bronze.dashboard')); ?>" class="flex items-center px-3 py-2 rounded-lg hover:bg-white/10 transition-colors text-sm">
                        <i class="fas fa-home mr-3 w-4"></i>Dashboard
                    </a>
                    <a href="<?php echo e(url('/bronze-plan/profile')); ?>" class="flex items-center px-3 py-2 rounded-lg hover:bg-white/10 transition-colors text-sm">
                        <i class="fas fa-user mr-3 w-4"></i>Profile
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 rounded-lg bg-accent-blue/20 text-accent-blue transition-colors text-sm">
                        <i class="fas fa-cog mr-3 w-4"></i>Settings
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Overlay for mobile -->
        <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-30 bg-black/50 lg:hidden" x-transition style="top: 64px;"></div>

        <!-- Main Content -->
        <main class="content-area">
            <div class="main-content-scroll">
                <div class="p-3 sm:p-4 lg:p-6">
                    <!-- Header -->
                    <div class="mb-6 sm:mb-8">
                        <h1 class="text-2xl sm:text-3xl font-bold mb-2">Settings</h1>
                        <p class="text-gray-300 text-sm sm:text-base">Manage your account preferences and security settings</p>
                    </div>

                    <!-- Settings Tabs -->
                    <div class="mb-6">
                        <div class="flex flex-wrap gap-2 border-b border-gray-600">
                            <button @click="activeTab = 'account'" 
                                    :class="activeTab === 'account' ? 'active' : ''"
                                    class="tab-button px-4 py-2 rounded-t-lg border-b-2 border-transparent hover:bg-white/5 transition-colors">
                                <i class="fas fa-user mr-2"></i>Account
                            </button>
                            <button @click="activeTab = 'security'" 
                                    :class="activeTab === 'security' ? 'active' : ''"
                                    class="tab-button px-4 py-2 rounded-t-lg border-b-2 border-transparent hover:bg-white/5 transition-colors">
                                <i class="fas fa-shield-alt mr-2"></i>Security
                            </button>
                            <button @click="activeTab = 'notifications'" 
                                    :class="activeTab === 'notifications' ? 'active' : ''"
                                    class="tab-button px-4 py-2 rounded-t-lg border-b-2 border-transparent hover:bg-white/5 transition-colors">
                                <i class="fas fa-bell mr-2"></i>Notifications
                            </button>
                            <button @click="activeTab = 'billing'" 
                                    :class="activeTab === 'billing' ? 'active' : ''"
                                    class="tab-button px-4 py-2 rounded-t-lg border-b-2 border-transparent hover:bg-white/5 transition-colors">
                                <i class="fas fa-credit-card mr-2"></i>Billing
                            </button>
                        </div>
                    </div>

                    <!-- Account Settings -->
                    <div x-show="activeTab === 'account'" x-transition class="space-y-6">
                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4">Account Information</h2>
                            <form class="space-y-4">
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Username</label>
                                        <input type="text" value="johndoe" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Display Name</label>
                                        <input type="text" value="John Doe" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                                    <input type="email" value="<EMAIL>" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Timezone</label>
                                    <select class="w-full px-4 py-3 rounded-lg form-input text-white">
                                        <option>Asia/Jakarta (UTC+7)</option>
                                        <option>Asia/Singapore (UTC+8)</option>
                                        <option>UTC (UTC+0)</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn-primary py-3 px-6 rounded-lg font-semibold">
                                    <i class="fas fa-save mr-2"></i>Save Changes
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div x-show="activeTab === 'security'" x-transition class="space-y-6">
                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4">Change Password</h2>
                            <form class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Current Password</label>
                                    <input type="password" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">New Password</label>
                                    <input type="password" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Confirm New Password</label>
                                    <input type="password" class="w-full px-4 py-3 rounded-lg form-input text-white">
                                </div>
                                <button type="submit" class="btn-primary py-3 px-6 rounded-lg font-semibold">
                                    <i class="fas fa-key mr-2"></i>Update Password
                                </button>
                            </form>
                        </div>

                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4">Two-Factor Authentication</h2>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium">Enable 2FA</p>
                                    <p class="text-sm text-gray-400">Add an extra layer of security to your account</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div x-show="activeTab === 'notifications'" x-transition class="space-y-6">
                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4">Email Notifications</h2>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium">Link Analytics</p>
                                        <p class="text-sm text-gray-400">Receive weekly analytics reports</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium">Security Alerts</p>
                                        <p class="text-sm text-gray-400">Get notified about account security events</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium">Marketing Updates</p>
                                        <p class="text-sm text-gray-400">Receive news and product updates</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Billing Settings -->
                    <div x-show="activeTab === 'billing'" x-transition class="space-y-6">
                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4">Current Plan</h2>
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <p class="text-lg font-medium">Bronze Plan</p>
                                    <p class="text-sm text-gray-400">100 links • Basic analytics • Standard support</p>
                                </div>
                                <span class="px-3 py-1 bg-yellow-600 text-sm rounded-full">Active</span>
                            </div>
                            <a href="/#pricing" class="btn-primary py-3 px-6 rounded-lg font-semibold inline-block">
                                <i class="fas fa-crown mr-2"></i>Upgrade Plan
                            </a>
                        </div>

                        <div class="glass-effect rounded-xl p-6">
                            <h2 class="text-xl font-semibold mb-4 text-red-400">Danger Zone</h2>
                            <div class="space-y-4">
                                <div class="border border-red-500/30 rounded-lg p-4">
                                    <h3 class="font-medium text-red-400 mb-2">Delete Account</h3>
                                    <p class="text-sm text-gray-400 mb-4">Once you delete your account, there is no going back. Please be certain.</p>
                                    <button class="btn-danger py-2 px-4 rounded-lg font-semibold text-sm">
                                        <i class="fas fa-trash mr-2"></i>Delete Account
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
<?php /**PATH C:\laragon\www\sub4short-plus\resources\views/bronze-plan/pages/settings.blade.php ENDPATH**/ ?>