<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sub4Short Plus</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-blue': '#0f172a',
                        'glass-blue': 'rgba(30, 58, 138, 0.1)',
                        'accent-blue': '#3b82f6'
                    }
                }
            }
        }
    </script>
    <style>
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(30, 58, 138, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-dark-blue via-slate-900 to-blue-900 text-white min-h-screen flex items-center justify-center p-4">
    
    <div class="w-full max-w-md">
        <!-- Back to Home -->
        <div class="mb-6">
            <a href="/" class="inline-flex items-center text-gray-300 hover:text-accent-blue transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Kembali ke Beranda
            </a>
        </div>

        <!-- Login Card -->
        <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <!-- Logo & Title -->
            <div class="text-center mb-8">
                <div class="flex items-center justify-center mb-4">
                    <img src="https://via.placeholder.com/50x50/3b82f6/ffffff?text=S4S" alt="Sub4Short Plus" class="h-12 w-12 rounded-lg mr-3">
                    <span class="text-2xl font-bold gradient-text">Sub4Short Plus</span>
                </div>
                <h1 class="text-2xl font-bold mb-2">Selamat Datang Kembali!</h1>
                <p class="text-gray-300">Masuk ke akun Anda untuk melanjutkan</p>
            </div>

            <!-- Login Form -->
            <form method="POST" action="<?php echo e(route('login')); ?>" x-data="{ showPassword: false }">
                <?php echo csrf_field(); ?>
                
                <!-- Email -->
                <div class="mb-6">
                    <label for="email" class="block text-sm font-medium mb-2">
                        <i class="fas fa-envelope mr-2 text-accent-blue"></i>Email
                    </label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           value="<?php echo e(old('email')); ?>"
                           required 
                           autofocus
                           class="w-full px-4 py-3 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400"
                           placeholder="Masukkan email Anda">
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-2 text-sm text-red-400"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Password -->
                <div class="mb-6">
                    <label for="password" class="block text-sm font-medium mb-2">
                        <i class="fas fa-lock mr-2 text-accent-blue"></i>Password
                    </label>
                    <div class="relative">
                        <input :type="showPassword ? 'text' : 'password'" 
                               id="password" 
                               name="password" 
                               required
                               class="w-full px-4 py-3 pr-12 rounded-lg bg-white/10 border border-blue-500/30 focus:border-accent-blue focus:outline-none focus:ring-2 focus:ring-accent-blue/20 transition-all placeholder-gray-400"
                               placeholder="Masukkan password Anda">
                        <button type="button" 
                                @click="showPassword = !showPassword"
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-accent-blue transition-colors">
                            <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                        </button>
                    </div>
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-2 text-sm text-red-400"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Remember Me & Forgot Password -->
                <div class="flex items-center justify-between mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" 
                               name="remember" 
                               class="w-4 h-4 text-accent-blue bg-white/10 border-blue-500/30 rounded focus:ring-accent-blue focus:ring-2">
                        <span class="ml-2 text-sm text-gray-300">Ingat saya</span>
                    </label>
                    <a href="<?php echo e(route('password.request')); ?>" class="text-sm text-accent-blue hover:text-blue-300 transition-colors">
                        Lupa password?
                    </a>
                </div>

                <!-- Login Button -->
                <button type="submit" 
                        class="w-full bg-accent-blue hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-accent-blue focus:ring-offset-2 focus:ring-offset-transparent">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Masuk
                </button>
            </form>

            <!-- Divider -->
            <div class="my-8 flex items-center">
                <div class="flex-1 border-t border-gray-600"></div>
                <span class="px-4 text-gray-400 text-sm">atau</span>
                <div class="flex-1 border-t border-gray-600"></div>
            </div>

            <!-- Social Login -->
            <div class="space-y-3 mb-6">
                <button class="w-full flex items-center justify-center px-4 py-3 border border-gray-600 rounded-lg hover:bg-white/5 transition-colors">
                    <i class="fab fa-google mr-3 text-red-400"></i>
                    Masuk dengan Google
                </button>
                <button class="w-full flex items-center justify-center px-4 py-3 border border-gray-600 rounded-lg hover:bg-white/5 transition-colors">
                    <i class="fab fa-facebook mr-3 text-blue-400"></i>
                    Masuk dengan Facebook
                </button>
            </div>

            <!-- Register Link -->
            <div class="text-center">
                <p class="text-gray-300">
                    Belum punya akun? 
                    <a href="<?php echo e(route('register')); ?>" class="text-accent-blue hover:text-blue-300 font-semibold transition-colors">
                        Daftar sekarang
                    </a>
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-gray-400 text-sm">
            <p>&copy; 2024 Sub4Short Plus. All rights reserved.</p>
        </div>
    </div>

</body>
</html>
<?php /**PATH C:\laragon\www\sub4short-plus\resources\views/auth/login.blade.php ENDPATH**/ ?>