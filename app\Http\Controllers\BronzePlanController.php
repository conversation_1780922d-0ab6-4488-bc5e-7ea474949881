<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class BronzePlanController extends Controller
{
    /**
     * Show the Bronze plan dashboard
     */
    public function dashboard(Request $request): View
    {
        $page = $request->get('page', 'dashboard');
        
        // Validate allowed pages
        $allowedPages = ['dashboard', 'my-links', 'analytics', 'qr-codes', 'withdraw'];
        
        if (!in_array($page, $allowedPages)) {
            $page = 'dashboard';
        }
        
        // Get user data (mock data for now)
        $userData = $this->getUserData();
        
        // Get page-specific data
        $pageData = $this->getPageData($page);
        
        return view('bronze-plan.dashboard', compact('page', 'userData', 'pageData'));
    }
    
    /**
     * Create a new short link
     */
    public function createLink(Request $request): JsonResponse
    {
        $request->validate([
            'url' => 'required|url|max:2048',
            'custom_alias' => 'nullable|string|max:50|alpha_dash|unique:links,alias',
        ]);
        
        // Check Bronze plan limitations
        $userLinks = $this->getUserLinksCount();
        if ($userLinks >= 100) {
            return response()->json([
                'success' => false,
                'message' => 'Bronze plan limit reached (100 links). Upgrade to create more links.',
                'upgrade_required' => true
            ], 403);
        }
        
        // Mock link creation
        $shortCode = $request->custom_alias ?: $this->generateShortCode();
        
        return response()->json([
            'success' => true,
            'message' => 'Link created successfully!',
            'data' => [
                'short_url' => 's4s.plus/' . $shortCode,
                'original_url' => $request->url,
                'clicks' => 0,
                'created_at' => now()->format('Y-m-d H:i:s')
            ]
        ]);
    }
    
    /**
     * Generate QR code for a link
     */
    public function generateQR(Request $request): JsonResponse
    {
        $request->validate([
            'link_id' => 'required|integer',
            'size' => 'required|in:200,400,800',
            'format' => 'required|in:png,jpg'
        ]);
        
        // Mock QR generation
        return response()->json([
            'success' => true,
            'message' => 'QR code generated successfully!',
            'data' => [
                'qr_url' => 'https://api.qrserver.com/v1/create-qr-code/?size=' . $request->size . 'x' . $request->size . '&data=s4s.plus/abc123',
                'download_url' => '/bronze-plan/download-qr/' . $request->link_id,
                'format' => $request->format,
                'size' => $request->size
            ]
        ]);
    }
    
    /**
     * Request withdrawal
     */
    public function requestWithdrawal(Request $request): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:50000',
            'method' => 'required|in:bank_transfer,ewallet',
            'account_details' => 'required|string|max:500'
        ]);
        
        // Check available balance
        $balance = $this->getUserBalance();
        if ($balance < $request->amount) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient balance. Available: Rp ' . number_format($balance, 0, ',', '.')
            ], 400);
        }
        
        if ($request->amount < 50000) {
            return response()->json([
                'success' => false,
                'message' => 'Minimum withdrawal amount is Rp 50,000 for Bronze plan.'
            ], 400);
        }
        
        // Mock withdrawal request
        return response()->json([
            'success' => true,
            'message' => 'Withdrawal request submitted successfully! Processing time: 3-7 business days.',
            'data' => [
                'request_id' => 'WD' . time(),
                'amount' => $request->amount,
                'method' => $request->method,
                'status' => 'pending',
                'estimated_completion' => now()->addDays(5)->format('Y-m-d')
            ]
        ]);
    }
    
    /**
     * Delete a link
     */
    public function deleteLink(Request $request, $id): JsonResponse
    {
        // Mock link deletion
        return response()->json([
            'success' => true,
            'message' => 'Link deleted successfully!'
        ]);
    }
    
    /**
     * Get analytics data
     */
    public function getAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', '7d'); // 7d, 30d, 90d
        
        // Mock analytics data
        $analyticsData = [
            'total_clicks' => 1234,
            'unique_visitors' => 892,
            'top_countries' => [
                ['country' => 'Indonesia', 'percentage' => 67],
                ['country' => 'Malaysia', 'percentage' => 15],
                ['country' => 'Singapore', 'percentage' => 12],
                ['country' => 'Others', 'percentage' => 6]
            ],
            'daily_clicks' => [
                ['date' => '2024-01-01', 'clicks' => 45],
                ['date' => '2024-01-02', 'clicks' => 67],
                ['date' => '2024-01-03', 'clicks' => 89],
                ['date' => '2024-01-04', 'clicks' => 123],
                ['date' => '2024-01-05', 'clicks' => 156],
                ['date' => '2024-01-06', 'clicks' => 134],
                ['date' => '2024-01-07', 'clicks' => 178]
            ]
        ];
        
        return response()->json([
            'success' => true,
            'data' => $analyticsData
        ]);
    }
    
    /**
     * Get user data (mock)
     */
    private function getUserData(): array
    {
        return [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'plan' => 'Bronze',
            'links_used' => 45,
            'links_limit' => 100,
            'total_clicks' => 1234,
            'balance' => 0,
            'qr_codes_generated' => 23
        ];
    }
    
    /**
     * Get page-specific data
     */
    private function getPageData(string $page): array
    {
        switch ($page) {
            case 'my-links':
                return $this->getLinksData();
            case 'analytics':
                return $this->getAnalyticsData();
            case 'qr-codes':
                return $this->getQRCodesData();
            case 'withdraw':
                return $this->getWithdrawData();
            default:
                return $this->getDashboardData();
        }
    }
    
    /**
     * Get dashboard data
     */
    private function getDashboardData(): array
    {
        return [
            'recent_activities' => [
                ['type' => 'link_created', 'message' => 'New link created', 'time' => '2 hours ago'],
                ['type' => 'qr_generated', 'message' => 'QR code generated', 'time' => '5 hours ago'],
                ['type' => 'link_clicked', 'message' => 'Link clicked 15 times', 'time' => '1 day ago']
            ]
        ];
    }
    
    /**
     * Get links data
     */
    private function getLinksData(): array
    {
        return [
            'links' => [
                [
                    'id' => 1,
                    'short_code' => 'abc123',
                    'original_url' => 'https://example.com/very-long-url-here',
                    'clicks' => 156,
                    'created_at' => '2 days ago'
                ],
                [
                    'id' => 2,
                    'short_code' => 'xyz789',
                    'original_url' => 'https://example.com/another-page',
                    'clicks' => 89,
                    'created_at' => '5 days ago'
                ]
            ]
        ];
    }
    
    /**
     * Get analytics data
     */
    private function getAnalyticsData(): array
    {
        return [
            'total_views' => 1234,
            'unique_visitors' => 892,
            'top_country' => 'Indonesia',
            'ctr' => 3.2
        ];
    }
    
    /**
     * Get QR codes data
     */
    private function getQRCodesData(): array
    {
        return [
            'qr_codes' => [
                [
                    'id' => 1,
                    'link_code' => 'abc123',
                    'scans' => 89,
                    'created_at' => '2 days ago'
                ],
                [
                    'id' => 2,
                    'link_code' => 'xyz789',
                    'scans' => 67,
                    'created_at' => '5 days ago'
                ]
            ]
        ];
    }
    
    /**
     * Get withdraw data
     */
    private function getWithdrawData(): array
    {
        return [
            'available_balance' => 0,
            'pending_withdrawals' => 0,
            'total_withdrawn' => 0,
            'min_withdrawal' => 50000
        ];
    }
    
    /**
     * Generate random short code
     */
    private function generateShortCode(): string
    {
        return substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
    }
    
    /**
     * Get user links count (mock)
     */
    private function getUserLinksCount(): int
    {
        return 45; // Mock data
    }
    
    /**
     * Get user balance (mock)
     */
    private function getUserBalance(): int
    {
        return 0; // Mock data
    }
}
