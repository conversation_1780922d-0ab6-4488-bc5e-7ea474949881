<!-- Profile Page Content -->
<div x-data="profileManager()">
    <!-- Toastr Container -->
    <div id="toastr-container" class="fixed top-20 right-4 z-50 space-y-2"></div>

    <style>
        .profile-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(30, 41, 59, 0.9) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.4);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .form-input {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15);
            outline: none;
            background: rgba(30, 41, 59, 0.9);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: rgba(75, 85, 99, 0.9);
            border: 2px solid rgba(156, 163, 175, 0.4);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(107, 114, 128, 0.9);
            border-color: rgba(156, 163, 175, 0.6);
            transform: translateY(-1px);
        }

        .avatar-container {
            position: relative;
            display: inline-block;
        }

        .avatar-overlay {
            position: absolute;
            inset: 0;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .avatar-container:hover .avatar-overlay {
            opacity: 1;
        }

        .loading-spinner {
            border: 3px solid rgba(59, 130, 246, 0.3);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .toastr {
            padding: 16px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            transform: translateX(100%);
            animation: slideIn 0.3s ease forwards;
        }

        .toastr.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: 1px solid rgba(16, 185, 129, 0.5);
        }

        .toastr.error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            border: 1px solid rgba(239, 68, 68, 0.5);
        }

        @keyframes slideIn {
            to { transform: translateX(0); }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .avatar-glow {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
            animation: avatarGlow 2s ease-in-out infinite alternate;
        }

        @keyframes avatarGlow {
            from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
            to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.7); }
        }

        @keyframes slideOut {
            to { transform: translateX(100%); }
        }

        .stats-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(30, 41, 59, 0.8) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }
    </style>

    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-2">
                    Profile Settings
                </h1>
                <p class="text-gray-300">Manage your personal information and account details</p>
            </div>
            <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm text-gray-400">Online</span>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
        <!-- Profile Card -->
        <div class="xl:col-span-1">
            <div class="profile-card rounded-2xl p-8">
                <div class="text-center">
                    <!-- Avatar Upload Section -->
                    <div class="avatar-container mb-6">
                        <div class="relative">
                            <img :src="avatarPreview || currentAvatar"
                                 alt="Profile Picture"
                                 class="user-avatar w-32 h-32 rounded-full mx-auto border-4 border-blue-500/40 shadow-lg object-cover transition-all duration-500"
                                 :class="{ 'opacity-50': uploading, 'avatar-glow': justUploaded }"
                                 x-ref="avatarImage">

                            <!-- Loading Spinner -->
                            <div x-show="uploading" class="absolute inset-0 flex items-center justify-center">
                                <div class="loading-spinner w-8 h-8"></div>
                            </div>

                            <!-- Upload Overlay -->
                            <div class="avatar-overlay cursor-pointer" @click="$refs.avatarInput.click()">
                                <i class="fas fa-camera text-2xl text-white"></i>
                            </div>

                            <input type="file"
                                   x-ref="avatarInput"
                                   accept="image/*"
                                   @change="handleAvatarUpload($event)"
                                   class="hidden">
                        </div>
                    </div>

                    <h3 class="text-2xl font-bold mb-2 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                        John Doe
                    </h3>
                    <p class="text-gray-400 mb-3"><EMAIL></p>
                    <div class="flex items-center justify-center space-x-2 mb-4">
                        <span class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-sm rounded-full">
                            <i class="fas fa-crown mr-1"></i>
                            Bronze Plan
                        </span>
                    </div>

                    <!-- Quick Actions -->
                    <div class="flex space-x-2 mb-6">
                        <button @click="editMode = !editMode"
                                class="flex-1 py-2 px-4 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-sm font-medium">
                            <i class="fas fa-edit mr-1"></i>
                            <span x-text="editMode ? 'Cancel' : 'Edit'"></span>
                        </button>
                        <button class="py-2 px-4 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors text-sm">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="xl:col-span-3 grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
            <div class="stats-card rounded-xl p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-calendar text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-400 text-sm">Member since</p>
                        <p class="text-xl font-bold">Jan 2024</p>
                    </div>
                </div>
            </div>

            <div class="stats-card rounded-xl p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-link text-green-400 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-400 text-sm">Total Links</p>
                        <p class="text-xl font-bold">45</p>
                    </div>
                </div>
            </div>

            <div class="stats-card rounded-xl p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-mouse-pointer text-purple-400 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-400 text-sm">Total Clicks</p>
                        <p class="text-xl font-bold">1,234</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="xl:col-span-4">
            <div class="profile-card rounded-2xl p-8">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h3 class="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                            Personal Information
                        </h3>
                        <p class="text-gray-400 mt-1">Update your personal details and preferences</p>
                    </div>
                </div>

                <form @submit.prevent="saveProfile" class="space-y-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-user mr-2 text-blue-400"></i>First Name
                            </label>
                            <input type="text"
                                   x-model="profile.firstName"
                                   :disabled="!editMode"
                                   class="form-input w-full px-4 py-4 rounded-xl text-white placeholder-gray-400 text-lg"
                                   :class="editMode ? 'ring-2 ring-blue-500/20' : 'opacity-60 cursor-not-allowed'"
                                   placeholder="Enter your first name">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-user mr-2 text-blue-400"></i>Last Name
                            </label>
                            <input type="text"
                                   x-model="profile.lastName"
                                   :disabled="!editMode"
                                   class="form-input w-full px-4 py-4 rounded-xl text-white placeholder-gray-400 text-lg"
                                   :class="editMode ? 'ring-2 ring-blue-500/20' : 'opacity-60 cursor-not-allowed'"
                                   placeholder="Enter your last name">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-envelope mr-2 text-blue-400"></i>Email Address
                            </label>
                            <input type="email"
                                   x-model="profile.email"
                                   :disabled="!editMode"
                                   class="form-input w-full px-4 py-4 rounded-xl text-white placeholder-gray-400 text-lg"
                                   :class="editMode ? 'ring-2 ring-blue-500/20' : 'opacity-60 cursor-not-allowed'"
                                   placeholder="Enter your email">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-3">
                                <i class="fas fa-phone mr-2 text-blue-400"></i>Phone Number
                            </label>
                            <input type="tel"
                                   x-model="profile.phone"
                                   :disabled="!editMode"
                                   class="form-input w-full px-4 py-4 rounded-xl text-white placeholder-gray-400 text-lg"
                                   :class="editMode ? 'ring-2 ring-blue-500/20' : 'opacity-60 cursor-not-allowed'"
                                   placeholder="Enter your phone number">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-semibold text-gray-300 mb-3">
                            <i class="fas fa-info-circle mr-2 text-blue-400"></i>Bio
                        </label>
                        <textarea rows="4"
                                  x-model="profile.bio"
                                  :disabled="!editMode"
                                  class="form-input w-full px-4 py-4 rounded-xl text-white placeholder-gray-400 resize-none text-lg"
                                  :class="editMode ? 'ring-2 ring-blue-500/20' : 'opacity-60 cursor-not-allowed'"
                                  placeholder="Tell us about yourself..."></textarea>
                    </div>

                    <div x-show="editMode" class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 pt-6">
                        <button type="submit"
                                :disabled="saving"
                                class="btn-primary px-8 py-4 rounded-xl font-semibold text-lg flex items-center justify-center"
                                :class="saving ? 'opacity-50 cursor-not-allowed' : ''">
                            <div x-show="saving" class="loading-spinner w-5 h-5 mr-2"></div>
                            <i x-show="!saving" class="fas fa-save mr-2"></i>
                            <span x-text="saving ? 'Saving...' : 'Save Changes'"></span>
                        </button>
                        <button type="button"
                                @click="cancelEdit"
                                class="btn-secondary px-8 py-4 rounded-xl font-semibold text-lg">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </button>
                    </div>


                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript Functions -->
    <script>
        function profileManager() {
            return {
                editMode: false,
                uploading: false,
                saving: false,
                justUploaded: false,
                avatarPreview: null,
                currentAvatar: 'https://via.placeholder.com/128x128/3b82f6/ffffff?text=JD',
                profile: {
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    phone: '+62 812-3456-7890',
                    bio: 'Digital marketer and URL shortening enthusiast. Love creating efficient workflows and tracking analytics.'
                },
                originalProfile: {},

                init() {
                    this.originalProfile = { ...this.profile };

                    // Load saved avatar from localStorage
                    const savedAvatar = localStorage.getItem('user_avatar');
                    if (savedAvatar) {
                        this.currentAvatar = savedAvatar;
                        this.updateNavbarAvatar(savedAvatar);
                    }
                },

                async handleAvatarUpload(event) {
                    const file = event.target.files[0];
                    if (!file) return;

                    // Validate file type
                    if (!file.type.startsWith('image/')) {
                        this.showToastr('Please select a valid image file', 'error');
                        return;
                    }

                    // Validate file size (max 5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        this.showToastr('Image size must be less than 5MB', 'error');
                        return;
                    }

                    this.uploading = true;

                    try {
                        // Create preview
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            this.avatarPreview = e.target.result;
                        };
                        reader.readAsDataURL(file);

                        // Simulate upload delay
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        // Create FormData for upload
                        const formData = new FormData();
                        formData.append('avatar', file);
                        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

                        // Upload to server (you'll need to create this route)
                        const response = await fetch('/bronze-plan/upload-avatar', {
                            method: 'POST',
                            body: formData
                        });

                        if (response.ok) {
                            const result = await response.json();
                            this.currentAvatar = result.avatar_url;
                            this.avatarPreview = null;

                            // Save avatar to localStorage
                            localStorage.setItem('user_avatar', result.avatar_url);

                            // Update avatar in navbar dropdown
                            this.updateNavbarAvatar(result.avatar_url);

                            // Also call global function if available
                            if (window.updateAllAvatars) {
                                window.updateAllAvatars(result.avatar_url);
                            }

                            // Add glow effect
                            this.justUploaded = true;
                            setTimeout(() => {
                                this.justUploaded = false;
                            }, 3000);

                            // Play success sound
                            this.playSuccessSound();
                            this.showToastr('Avatar updated successfully!', 'success');
                        } else {
                            throw new Error('Upload failed');
                        }
                    } catch (error) {
                        console.error('Upload error:', error);
                        this.avatarPreview = null;
                        this.showToastr('Failed to upload avatar. Please try again.', 'error');
                    } finally {
                        this.uploading = false;
                    }
                },

                async saveProfile() {
                    this.saving = true;

                    try {
                        // Simulate save delay
                        await new Promise(resolve => setTimeout(resolve, 1500));

                        // Here you would make an API call to save the profile
                        // const response = await fetch('/bronze-plan/update-profile', {
                        //     method: 'POST',
                        //     headers: {
                        //         'Content-Type': 'application/json',
                        //         'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        //     },
                        //     body: JSON.stringify(this.profile)
                        // });

                        this.originalProfile = { ...this.profile };
                        this.editMode = false;

                        this.playSuccessSound();
                        this.showToastr('Profile updated successfully!', 'success');
                    } catch (error) {
                        console.error('Save error:', error);
                        this.showToastr('Failed to save profile. Please try again.', 'error');
                    } finally {
                        this.saving = false;
                    }
                },

                cancelEdit() {
                    this.profile = { ...this.originalProfile };
                    this.editMode = false;
                },



                playSuccessSound() {
                    // Create audio context for success sound
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
                    oscillator.frequency.setValueAtTime(1200, audioContext.currentTime + 0.2);

                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.3);
                },

                updateNavbarAvatar(avatarUrl) {
                    // Update avatar in navbar dropdown with multiple selectors
                    const navbarAvatar = document.querySelector('#navbar-avatar');
                    if (navbarAvatar) {
                        navbarAvatar.src = avatarUrl;
                    }

                    // Update all avatar instances in the page
                    const allAvatars = document.querySelectorAll('.user-avatar');
                    allAvatars.forEach(avatar => {
                        avatar.src = avatarUrl;
                    });

                    // Also try with class selector as backup
                    const navbarAvatarClass = document.querySelector('.navbar-avatar');
                    if (navbarAvatarClass) {
                        navbarAvatarClass.src = avatarUrl;
                    }
                },

                showToastr(message, type = 'success') {
                    const container = document.getElementById('toastr-container');
                    const toastr = document.createElement('div');
                    toastr.className = `toastr ${type}`;
                    toastr.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-3 text-lg"></i>
                            <span>${message}</span>
                        </div>
                    `;

                    container.appendChild(toastr);

                    // Auto remove after 4 seconds
                    setTimeout(() => {
                        toastr.style.animation = 'slideOut 0.3s ease forwards';
                        setTimeout(() => {
                            if (container.contains(toastr)) {
                                container.removeChild(toastr);
                            }
                        }, 300);
                    }, 4000);
                }
            }
        }
    </script>
</div>
