<!-- Profile Page Content -->
<div x-data="{ editMode: false, avatarPreview: null }">
    <style>
        .avatar-upload {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }

        .avatar-upload input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .profile-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(30, 41, 59, 0.8) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .form-input {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .form-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: rgba(75, 85, 99, 0.8);
            border: 1px solid rgba(156, 163, 175, 0.3);
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: rgba(107, 114, 128, 0.8);
            border-color: rgba(156, 163, 175, 0.5);
        }
    </style>

    <!-- Header -->
    <div class="mb-6 sm:mb-8">
        <h1 class="text-2xl sm:text-3xl font-bold mb-2">Profile Settings</h1>
        <p class="text-gray-300 text-sm sm:text-base">Manage your personal information and account details</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Header -->
                    <div class="mb-6 sm:mb-8">
                        <h1 class="text-2xl sm:text-3xl font-bold mb-2">Profile Settings</h1>
                        <p class="text-gray-300 text-sm sm:text-base">Manage your personal information and account details</p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Profile Card -->
                        <div class="lg:col-span-1">
                            <div class="profile-card rounded-xl p-6">
                                <div class="text-center">
                                    <div class="avatar-upload mb-4">
                                        <div class="relative">
                                            <img :src="avatarPreview || 'https://via.placeholder.com/120x120/3b82f6/ffffff?text=JD'" 
                                                 alt="Profile Picture" 
                                                 class="w-24 h-24 sm:w-32 sm:h-32 rounded-full mx-auto border-4 border-accent-blue/30">
                                            <label for="avatar-upload" class="absolute bottom-0 right-1/2 transform translate-x-1/2 translate-y-1/2 bg-accent-blue hover:bg-blue-600 text-white p-2 rounded-full cursor-pointer transition-colors">
                                                <i class="fas fa-camera text-sm"></i>
                                            </label>
                                            <input type="file" id="avatar-upload" accept="image/*" @change="handleAvatarUpload">
                                        </div>
                                    </div>
                                    <h3 class="text-xl font-semibold mb-1">John Doe</h3>
                                    <p class="text-gray-400 mb-2"><EMAIL></p>
                                    <span class="inline-block px-3 py-1 bg-yellow-600 text-xs rounded-full">Bronze Plan</span>
                                </div>
                                
                                <div class="mt-6 space-y-3">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-400">Member since</span>
                                        <span>January 2024</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-400">Total Links</span>
                                        <span>45</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-400">Total Clicks</span>
                                        <span>1,234</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Form -->
                        <div class="lg:col-span-2">
                            <div class="glass-effect rounded-xl p-6">
                                <div class="flex justify-between items-center mb-6">
                                    <h2 class="text-xl font-semibold">Personal Information</h2>
                                    <button @click="editMode = !editMode" 
                                            class="px-4 py-2 rounded-lg transition-colors text-sm"
                                            :class="editMode ? 'btn-secondary' : 'btn-primary'">
                                        <i class="fas mr-2" :class="editMode ? 'fa-times' : 'fa-edit'"></i>
                                        <span x-text="editMode ? 'Cancel' : 'Edit Profile'"></span>
                                    </button>
                                </div>

                                <form class="space-y-6">
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-300 mb-2">First Name</label>
                                            <input type="text" value="John" 
                                                   :disabled="!editMode"
                                                   class="w-full px-4 py-3 rounded-lg form-input text-white placeholder-gray-400 disabled:opacity-60 disabled:cursor-not-allowed">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-300 mb-2">Last Name</label>
                                            <input type="text" value="Doe" 
                                                   :disabled="!editMode"
                                                   class="w-full px-4 py-3 rounded-lg form-input text-white placeholder-gray-400 disabled:opacity-60 disabled:cursor-not-allowed">
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                                        <input type="email" value="<EMAIL>" 
                                               :disabled="!editMode"
                                               class="w-full px-4 py-3 rounded-lg form-input text-white placeholder-gray-400 disabled:opacity-60 disabled:cursor-not-allowed">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                                        <input type="tel" value="+62 812-3456-7890" 
                                               :disabled="!editMode"
                                               class="w-full px-4 py-3 rounded-lg form-input text-white placeholder-gray-400 disabled:opacity-60 disabled:cursor-not-allowed">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Bio</label>
                                        <textarea rows="4" 
                                                  :disabled="!editMode"
                                                  placeholder="Tell us about yourself..."
                                                  class="w-full px-4 py-3 rounded-lg form-input text-white placeholder-gray-400 disabled:opacity-60 disabled:cursor-not-allowed resize-none">Digital marketer and content creator passionate about technology and innovation.</textarea>
                                    </div>

                                    <div x-show="editMode" x-transition class="flex space-x-4">
                                        <button type="submit" class="flex-1 btn-primary py-3 px-6 rounded-lg font-semibold">
                                            <i class="fas fa-save mr-2"></i>Save Changes
                                        </button>
                                        <button type="button" @click="editMode = false" class="flex-1 btn-secondary py-3 px-6 rounded-lg font-semibold">
                                            <i class="fas fa-times mr-2"></i>Cancel
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
    </div>

    <script>
        function handleAvatarUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.querySelector('[x-data]').__x.$data.avatarPreview = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        }
    </script>
</div>
